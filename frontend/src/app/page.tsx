'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import Sidebar from '@/components/Sidebar';
import MainContent from '@/components/MainContent';
import Footer from '@/components/Footer';

export default function Home() {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false); // Start with false to prevent hydration mismatch
  const [selectedLeagueId, setSelectedLeagueId] = useState<number | null>(null);

  // Handle responsive sidebar
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768) {
        setIsSidebarOpen(false);
      } else {
        setIsSidebarOpen(true);
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const handleLeagueSelect = (leagueId: number) => {
    setSelectedLeagueId(selectedLeagueId === leagueId ? null : leagueId);
  };

  const handleClearLeagueFilter = () => {
    setSelectedLeagueId(null);
  };



  return (
    <div className="min-h-screen bg-background">
      <Header onToggleSidebar={toggleSidebar} />

      <div className="flex gap-2">
        {/* Sidebar */}
        <div className={`${isSidebarOpen ? 'block' : 'hidden'} md:block`}>
          <Sidebar
            onLeagueSelect={handleLeagueSelect}
            selectedLeagueId={selectedLeagueId}
          />
        </div>

        {/* Main Content */}
        <div className="flex-1">
          <MainContent
            selectedLeagueId={selectedLeagueId}
            onClearLeagueFilter={handleClearLeagueFilter}
          />
        </div>
      </div>

      <Footer />
    </div>
  );
}
