'use client';

import { useState, useEffect } from 'react';
import Header from '@/components/Header';
import Sidebar from '@/components/Sidebar';
import MainContent from '@/components/MainContent';
import Footer from '@/components/Footer';

export default function Home() {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false); // Start with false to prevent hydration mismatch
  const [selectedLeagueId, setSelectedLeagueId] = useState<number | null>(null);
  const [mounted, setMounted] = useState(false);

  // Handle responsive sidebar
  useEffect(() => {
    setMounted(true);

    const handleResize = () => {
      if (window.innerWidth < 768) {
        setIsSidebarOpen(false);
      } else {
        setIsSidebarOpen(true);
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const handleLeagueSelect = (leagueId: number) => {
    setSelectedLeagueId(selectedLeagueId === leagueId ? null : leagueId);
  };

  const handleClearLeagueFilter = () => {
    setSelectedLeagueId(null);
  };

  // Prevent hydration mismatch by not rendering until mounted
  if (!mounted) {
    return (
      <div className="min-h-screen bg-background">
        <div className="animate-pulse">
          <div className="h-16 bg-card border-b border-border"></div>
          <div className="flex">
            <div className="w-80 h-screen bg-card border-r border-border"></div>
            <div className="flex-1 p-6">
              <div className="space-y-4">
                <div className="h-8 bg-muted rounded w-1/3"></div>
                <div className="h-32 bg-muted rounded"></div>
                <div className="h-32 bg-muted rounded"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Header onToggleSidebar={toggleSidebar} />

      <div className="flex gap-2">
        {/* Sidebar */}
        <div className={`${isSidebarOpen ? 'block' : 'hidden'} md:block`}>
          <Sidebar
            onLeagueSelect={handleLeagueSelect}
            selectedLeagueId={selectedLeagueId}
          />
        </div>

        {/* Main Content */}
        <div className="flex-1">
          <MainContent
            selectedLeagueId={selectedLeagueId}
            onClearLeagueFilter={handleClearLeagueFilter}
          />
        </div>
      </div>

      <Footer />
    </div>
  );
}
