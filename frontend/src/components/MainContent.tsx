'use client';

import { useState, useEffect } from 'react';
import { ChevronLeft, ChevronRight, Search } from 'lucide-react';
import { format, addDays, subDays, isToday } from 'date-fns';
import { apiService, Fixture, handleApiError } from '@/lib/api';
import { useLiveFixtures } from '@/hooks/useSocket';
import { groupFixturesByLeague, LeagueGroup } from '@/lib/leagueTiers';

// Remove the local Fixture interface since we're importing it from api.ts

interface MainContentProps {
  selectedLeagueId?: number | null;
  onClearLeagueFilter?: () => void;
}

export default function MainContent({ selectedLeagueId, onClearLeagueFilter }: MainContentProps) {
  const [selectedDate, setSelectedDate] = useState(() => new Date());
  const [fixtures, setFixtures] = useState<Fixture[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [mounted, setMounted] = useState(false);

  // Use WebSocket for live fixtures
  const { liveFixtures, connected } = useLiveFixtures();

  // Set mounted state to prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  // Generate date navigation array
  const generateDateArray = () => {
    const dates = [];
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Normalize to start of day
    for (let i = -2; i <= 2; i++) {
      dates.push(addDays(today, i));
    }
    return dates;
  };

  const dateArray = generateDateArray();

  // Fetch fixtures data from API
  useEffect(() => {
    const fetchFixtures = async () => {
      setLoading(true);
      setError(null);

      try {
        const dateString = format(selectedDate, 'yyyy-MM-dd');
        const fixturesData = await apiService.getFixtures(dateString);
        setFixtures(fixturesData);
      } catch (err) {
        const errorMessage = handleApiError(err);
        setError(errorMessage);
        console.error('Failed to fetch fixtures:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchFixtures();
  }, [selectedDate]);

  // Update fixtures with live data when available
  useEffect(() => {
    if (liveFixtures.length > 0) {
      setFixtures(prevFixtures => {
        const updatedFixtures = [...prevFixtures];

        liveFixtures.forEach(liveFixture => {
          const index = updatedFixtures.findIndex(f => f._id === liveFixture._id);
          if (index !== -1) {
            updatedFixtures[index] = liveFixture;
          }
        });

        return updatedFixtures;
      });
    }
  }, [liveFixtures]);

  // Filter fixtures based on search query and selected league
  const filteredFixtures = fixtures.filter(fixture => {
    // Filter by league if one is selected
    if (selectedLeagueId && fixture.league.id !== selectedLeagueId) {
      return false;
    }

    // Filter by search query if one exists
    if (!searchQuery) return true;

    const query = searchQuery.toLowerCase();
    return (
      fixture.teams.home.name.toLowerCase().includes(query) ||
      fixture.teams.away.name.toLowerCase().includes(query) ||
      fixture.league.name.toLowerCase().includes(query) ||
      fixture.league.country.toLowerCase().includes(query)
    );
  });

  // Group fixtures by league and sort by tier/priority
  const groupedFixtures = groupFixturesByLeague(filteredFixtures);

  const handleDateChange = (date: Date) => {
    setSelectedDate(date);
  };

  const handlePrevDate = () => {
    setSelectedDate(subDays(selectedDate, 1));
  };

  const handleNextDate = () => {
    setSelectedDate(addDays(selectedDate, 1));
  };

  // Function to get country flag URL using API-Sports media
  const getCountryFlagUrl = (countryName: string) => {
    // Convert country name to country code for API-Sports flags
    const countryMappings: { [key: string]: string } = {
      'england': 'gb',
      'scotland': 'gb',
      'wales': 'gb',
      'northern ireland': 'gb',
      'united kingdom': 'gb',
      'usa': 'us',
      'united states': 'us',
      'south korea': 'kr',
      'north korea': 'kp',
      'czech republic': 'cz',
      'bosnia and herzegovina': 'ba',
      'trinidad and tobago': 'tt',
      'costa rica': 'cr',
      'el salvador': 'sv',
      'saudi arabia': 'sa',
      'united arab emirates': 'ae',
      'ivory coast': 'ci',
      'south africa': 'za',
      'world': 'fifa'
    };

    const countryCode = countryMappings[countryName.toLowerCase()] ||
                       countryName.toLowerCase().replace(/\s+/g, '-').substring(0, 2);

    return `https://media.api-sports.io/flags/${countryCode}.svg`;
  };

  const LeagueHeader = ({ leagueGroup }: { leagueGroup: LeagueGroup }) => (
    <div className="flex items-center space-x-2 py-2 px-4 bg-muted border-b border-border">
      <img
        src={getCountryFlagUrl(leagueGroup.leagueCountry)}
        alt={leagueGroup.leagueCountry}
        className="w-5 h-5 object-cover rounded-full border border-border"
        onError={(e) => {
          // Keep the same circular styling even for fallback
          e.currentTarget.src = leagueGroup.leagueLogo;
          e.currentTarget.className = "w-5 h-5 object-cover rounded-full border border-border";
        }}
      />
      <h3 className="text-sm font-medium text-foreground">{leagueGroup.leagueCountry} - {leagueGroup.leagueName}</h3>
    </div>
  );

  // Skeleton component for loading fixture cards
  const FixtureCardSkeleton = () => (
    <div className="bg-card border-b border-border">
      <div className="flex items-center py-3 px-4">
        {/* Favorite Icon and Status Column */}
        <div className="flex flex-col items-center mr-3">
          <div className="w-4 h-4 bg-muted rounded animate-pulse mb-1"></div>
          <div className="w-8 h-3 bg-muted rounded animate-pulse"></div>
        </div>

        {/* Teams Column */}
        <div className="flex-1 min-w-0">
          {/* Home Team Row */}
          <div className="flex items-center justify-between space-x-2 mb-1">
            <div className="flex items-center space-x-2 flex-1 min-w-0">
              <div className="w-6 h-6 bg-muted rounded animate-pulse flex-shrink-0"></div>
              <div className="h-4 bg-muted rounded animate-pulse w-24"></div>
            </div>
            <div className="w-4 h-4 bg-muted rounded animate-pulse flex-shrink-0"></div>
          </div>

          {/* Away Team Row */}
          <div className="flex items-center justify-between space-x-2">
            <div className="flex items-center space-x-2 flex-1 min-w-0">
              <div className="w-6 h-6 bg-muted rounded animate-pulse flex-shrink-0"></div>
              <div className="h-4 bg-muted rounded animate-pulse w-20"></div>
            </div>
            <div className="w-4 h-4 bg-muted rounded animate-pulse flex-shrink-0"></div>
          </div>
        </div>

        {/* Statistics/Odds Columns */}
        <div className="flex items-center space-x-6 ml-6">
          <div className="min-w-[60px] pl-2">
            <div className="h-3 bg-muted rounded animate-pulse w-16"></div>
          </div>
          <div className="min-w-[40px]">
            <div className="h-4 bg-muted rounded animate-pulse w-8"></div>
          </div>
          <div className="min-w-[40px]">
            <div className="h-4 bg-muted rounded animate-pulse w-8"></div>
          </div>
        </div>
      </div>
    </div>
  );

  const FixtureCard = ({ fixture, grouped = false }: { fixture: Fixture; grouped?: boolean }) => {
    // Function to get display status/time
    const getStatusDisplay = () => {
      const status = fixture.fixture.status.short;
      const elapsed = fixture.fixture.status.elapsed;

      // Live statuses with elapsed time
      if ((status === '1H' || status === '2H' || status === 'LIVE') && elapsed) {
        return `${elapsed}'`;
      }

      // Other match statuses
      if (status === 'HT') return 'HT';
      if (status === 'FT') return 'FT';
      if (status === 'AET') return 'AET';
      if (status === 'PEN') return 'PEN';
      if (status === 'PST') return 'PST';
      if (status === 'CANC') return 'CANC';
      if (status === 'ABD') return 'ABD';
      if (status === 'AWD') return 'AWD';
      if (status === 'WO') return 'WO';
      if (status === 'TBD') return 'TBD';

      // For upcoming matches (NS), show time
      if (status === 'NS') {
        return mounted ? format(new Date(fixture.fixture.date), 'HH:mm') : '';
      }

      return status;
    };

    // Function to check if status should be red (live/in-play)
    const isLiveStatus = () => {
      const status = fixture.fixture.status.short;
      return ['1H', '2H', 'LIVE', 'HT', 'ET', 'BT', 'P', 'SUSP', 'INT'].includes(status);
    };

    return (
      <div className="bg-card border-b border-border hover:bg-muted/50 transition-colors">
        <div className="flex items-center py-3 px-4">
          {/* Favorite Icon and Status Column */}
          <div className="flex flex-col items-center mr-3">
            {/* Favorite Icon */}
            <button className="text-muted-foreground hover:text-yellow-500 transition-colors mb-1">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
              </svg>
            </button>
            {/* Status/Time Display */}
            <div className={`text-xs font-medium text-center min-w-[32px] ${
              isLiveStatus() ? 'text-red-500' : 'text-muted-foreground'
            }`}>
              {getStatusDisplay()}
            </div>
          </div>

          {/* Teams Column */}
          <div className="flex-1 min-w-0">
            {/* Home Team Row */}
            <div className="flex items-center justify-between space-x-2 mb-1">
              <div className="flex items-center space-x-2 flex-1 min-w-0">
                <img
                  src={fixture.teams.home.logo}
                  alt={fixture.teams.home.name}
                  className="w-6 h-6 object-contain flex-shrink-0"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                  }}
                />
                <span className="text-sm text-foreground truncate">{fixture.teams.home.name}</span>
              </div>
              {/* Home Score */}
              <div className="text-sm font-semibold text-foreground ml-2 flex-shrink-0">
                {fixture.goals.home !== null ? fixture.goals.home : '-'}
              </div>
            </div>

            {/* Away Team Row */}
            <div className="flex items-center justify-between space-x-2">
              <div className="flex items-center space-x-2 flex-1 min-w-0">
                <img
                  src={fixture.teams.away.logo}
                  alt={fixture.teams.away.name}
                  className="w-6 h-6 object-contain flex-shrink-0"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                  }}
                />
                <span className="text-sm text-foreground truncate">{fixture.teams.away.name}</span>
              </div>
              {/* Away Score */}
              <div className="text-sm font-semibold text-foreground ml-2 flex-shrink-0">
                {fixture.goals.away !== null ? fixture.goals.away : '-'}
              </div>
            </div>
          </div>

          {/* Statistics/Odds Columns - Centered between teams */}
          <div className="flex items-center space-x-6 ml-6 text-xs">
            <div className="text-left min-w-[60px] pl-2">
              <div className="text-muted-foreground">Number of goals</div>
            </div>
            <div className="text-center min-w-[40px]">
              <div className="text-primary font-medium">-3.5</div>
            </div>
            <div className="text-right min-w-[40px]">
              <div className="text-foreground font-medium">1.12</div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Prevent hydration mismatch by not rendering until mounted
  if (!mounted) {
    return (
      <div className="flex-1 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="animate-pulse space-y-6">
            <div className="h-16 bg-muted rounded-lg"></div>
            <div className="h-32 bg-muted rounded-lg"></div>
            <div className="h-12 bg-muted rounded-lg"></div>
            <div className="space-y-4">
              <div className="h-24 bg-muted rounded-lg"></div>
              <div className="h-24 bg-muted rounded-lg"></div>
              <div className="h-24 bg-muted rounded-lg"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 p-6">
      <div className="max-w-4xl mx-auto">
        {/* Date Navigation and Search Bar Combined */}
        <div className="bg-card rounded-lg border border-border p-4 mb-6">
          {/* Date Navigation */}
          <div className="flex items-center justify-between mb-4">
            <button
              onClick={handlePrevDate}
              className="p-2 hover:bg-muted rounded-lg transition-colors"
            >
              <ChevronLeft className="w-5 h-5 text-muted-foreground" />
            </button>

            <div className="flex items-center space-x-2">
              {dateArray.map((date, index) => (
                <button
                  key={index}
                  onClick={() => handleDateChange(date)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    format(date, 'yyyy-MM-dd') === format(selectedDate, 'yyyy-MM-dd')
                      ? 'bg-primary text-primary-foreground'
                      : isToday(date)
                      ? 'bg-primary/10 text-primary hover:bg-primary/20'
                      : 'text-foreground hover:bg-muted'
                  }`}
                >
                  <div className="text-center">
                    <div className="text-xs">{format(date, 'EEE')}</div>
                    <div>{format(date, 'd')}</div>
                  </div>
                </button>
              ))}
            </div>

            <button
              onClick={handleNextDate}
              className="p-2 hover:bg-muted rounded-lg transition-colors"
            >
              <ChevronRight className="w-5 h-5 text-muted-foreground" />
            </button>
          </div>

          {/* Search Bar */}
          <div className="flex items-center bg-muted rounded-lg px-3 py-2">
            <Search className="w-4 h-4 text-muted-foreground mr-2" />
            <input
              type="text"
              placeholder="Search fixtures, teams, leagues..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="bg-transparent outline-none text-sm flex-1 text-foreground placeholder-muted-foreground"
            />
          </div>
        </div>

        {/* Betting Market Filter Tabs */}
        <div className="flex items-center space-x-1 mb-4">
          <button className="px-4 py-2 bg-primary text-primary-foreground text-sm rounded-lg font-medium">
            Top tips
          </button>
          <button className="px-4 py-2 bg-muted text-foreground text-sm rounded-lg font-medium hover:bg-muted/80 transition-colors">
            1X2
          </button>
          <button className="px-4 py-2 bg-muted text-foreground text-sm rounded-lg font-medium hover:bg-muted/80 transition-colors">
            Both teams to score
          </button>
          <button className="px-4 py-2 bg-muted text-foreground text-sm rounded-lg font-medium hover:bg-muted/80 transition-colors">
            Under/Over 2.5
          </button>
          <button className="px-4 py-2 bg-muted text-foreground text-sm rounded-lg font-medium hover:bg-muted/80 transition-colors">
            Double chance
          </button>
          <button className="px-4 py-2 bg-muted text-foreground text-sm rounded-lg font-medium hover:bg-muted/80 transition-colors">
            Under/Over 1.5
          </button>
          <button className="px-4 py-2 bg-muted text-foreground text-sm rounded-lg font-medium hover:bg-muted/80 transition-colors">
            Under/Over 3.5
          </button>
        </div>

        {/* Fixtures List */}
        <div className="space-y-4">
          {selectedLeagueId && (
            <div className="flex items-center justify-end">
              <button
                onClick={onClearLeagueFilter}
                className="text-sm text-primary hover:text-primary/80 font-medium"
              >
                Show All Leagues
              </button>
            </div>
          )}

          {loading ? (
            <div className="bg-card rounded-lg border border-border overflow-hidden">
              {/* Column Headers */}
              <div className="flex items-center py-2 px-4 bg-muted border-b border-border text-xs text-muted-foreground font-medium">
                <div className="mr-3 w-10"></div>
                <div className="flex-1"></div>
                <div className="flex items-center space-x-6 ml-6">
                  <div className="text-left min-w-[60px] pl-2">Market</div>
                  <div className="text-center min-w-[40px]">Value</div>
                  <div className="text-right min-w-[40px]">Odds</div>
                </div>
              </div>
              {/* Skeleton fixture cards */}
              {Array.from({ length: 8 }).map((_, index) => (
                <FixtureCardSkeleton key={`fixture-skeleton-${index}`} />
              ))}
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <div className="text-destructive mb-2">Error loading fixtures</div>
              <div className="text-muted-foreground text-sm">{error}</div>
              <button
                onClick={() => window.location.reload()}
                className="mt-4 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors"
              >
                Retry
              </button>
            </div>
          ) : groupedFixtures.length > 0 ? (
            <div className="bg-card rounded-lg border border-border overflow-hidden">
              {/* Column Headers */}
              <div className="flex items-center py-2 px-4 bg-muted border-b border-border text-xs text-muted-foreground font-medium">
                {/* Favorite Icon Column Space */}
                <div className="mr-3 w-10"></div>
                {/* Teams Column - No header text */}
                <div className="flex-1"></div>
                {/* Statistics Headers - Right aligned with data */}
                <div className="flex items-center space-x-6 ml-6">
                  <div className="text-left min-w-[60px] pl-2">Market</div>
                  <div className="text-center min-w-[40px]">Value</div>
                  <div className="text-right min-w-[40px]">Odds</div>
                </div>
              </div>

              {groupedFixtures.map((leagueGroup) => (
                <div key={leagueGroup.leagueId}>
                  <LeagueHeader leagueGroup={leagueGroup} />
                  <div>
                    {leagueGroup.fixtures.map((fixture) => (
                      <FixtureCard key={fixture._id || fixture.id} fixture={fixture} grouped={true} />
                    ))}
                  </div>
                </div>
              ))}
            </div>
          ) : searchQuery ? (
            <div className="text-center py-8">
              <div className="text-muted-foreground">No fixtures found matching "{searchQuery}"</div>
              <div className="text-muted-foreground/60 text-sm mt-2">
                Try a different search term or clear the search to see all fixtures.
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="text-muted-foreground">No fixtures found for this date.</div>
              <div className="text-muted-foreground/60 text-sm mt-2">
                Try selecting a different date or check back later.
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
