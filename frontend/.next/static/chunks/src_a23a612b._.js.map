{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/kickoffpredict/frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/kickoffpredict/frontend/src/components/ThemeToggle.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useTheme } from \"next-themes\"\nimport { Moon, Sun } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\nimport { useEffect, useState } from \"react\"\n\ninterface ThemeToggleProps {\n  className?: string\n}\n\nexport function ThemeToggle({ className }: ThemeToggleProps) {\n  const { resolvedTheme, setTheme } = useTheme()\n  const [mounted, setMounted] = useState(false)\n\n  useEffect(() => {\n    setMounted(true)\n  }, [])\n\n  if (!mounted) {\n    return (\n      <div className={cn(\"w-16 h-8 p-1 rounded-full bg-gray-200 border border-gray-300\", className)}>\n        <div className=\"w-6 h-6 bg-white rounded-full\"></div>\n      </div>\n    )\n  }\n\n  const isDark = resolvedTheme === \"dark\"\n\n  return (\n    <div\n      className={cn(\n        \"flex w-16 h-8 p-1 rounded-full cursor-pointer transition-all duration-300\",\n        isDark\n          ? \"bg-zinc-950 border border-zinc-800\"\n          : \"bg-white border border-zinc-200\",\n        className\n      )}\n      onClick={() => setTheme(isDark ? \"light\" : \"dark\")}\n      role=\"button\"\n      tabIndex={0}\n    >\n      <div className=\"flex justify-between items-center w-full\">\n        <div\n          className={cn(\n            \"flex justify-center items-center w-6 h-6 rounded-full transition-transform duration-300\",\n            isDark\n              ? \"transform translate-x-0 bg-zinc-800\"\n              : \"transform translate-x-8 bg-gray-200\"\n          )}\n        >\n          {isDark ? (\n            <Moon\n              className=\"w-4 h-4 text-white\"\n              strokeWidth={1.5}\n            />\n          ) : (\n            <Sun\n              className=\"w-4 h-4 text-gray-700\"\n              strokeWidth={1.5}\n            />\n          )}\n        </div>\n        <div\n          className={cn(\n            \"flex justify-center items-center w-6 h-6 rounded-full transition-transform duration-300\",\n            isDark\n              ? \"bg-transparent\"\n              : \"transform -translate-x-8\"\n          )}\n        >\n          {isDark ? (\n            <Sun\n              className=\"w-4 h-4 text-gray-500\"\n              strokeWidth={1.5}\n            />\n          ) : (\n            <Moon\n              className=\"w-4 h-4 text-black\"\n              strokeWidth={1.5}\n            />\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;;;AALA;;;;;AAWO,SAAS,YAAY,KAA+B;QAA/B,EAAE,SAAS,EAAoB,GAA/B;;IAC1B,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,WAAQ,AAAD;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,WAAW;QACb;gCAAG,EAAE;IAEL,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gEAAgE;sBACjF,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,MAAM,SAAS,kBAAkB;IAEjC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6EACA,SACI,uCACA,mCACJ;QAEF,SAAS,IAAM,SAAS,SAAS,UAAU;QAC3C,MAAK;QACL,UAAU;kBAEV,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2FACA,SACI,wCACA;8BAGL,uBACC,6LAAC,qMAAA,CAAA,OAAI;wBACH,WAAU;wBACV,aAAa;;;;;6CAGf,6LAAC,mMAAA,CAAA,MAAG;wBACF,WAAU;wBACV,aAAa;;;;;;;;;;;8BAInB,6LAAC;oBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2FACA,SACI,mBACA;8BAGL,uBACC,6LAAC,mMAAA,CAAA,MAAG;wBACF,WAAU;wBACV,aAAa;;;;;6CAGf,6LAAC,qMAAA,CAAA,OAAI;wBACH,WAAU;wBACV,aAAa;;;;;;;;;;;;;;;;;;;;;;AAO3B;GA3EgB;;QACsB,mJAAA,CAAA,WAAQ;;;KAD9B", "debugId": null}}, {"offset": {"line": 153, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/kickoffpredict/frontend/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\nimport { Search, Settings, Menu, User, LogIn } from 'lucide-react';\nimport { ThemeToggle } from './ThemeToggle';\n\ninterface HeaderProps {\n  onToggleSidebar?: () => void;\n}\n\nexport default function Header({ onToggleSidebar }: HeaderProps) {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [isSettingsOpen, setIsSettingsOpen] = useState(false);\n  const settingsRef = useRef<HTMLDivElement>(null);\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (settingsRef.current && !settingsRef.current.contains(event.target as Node)) {\n        setIsSettingsOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  return (\n    <header className=\"bg-card border-b border-border px-4 py-3\">\n      <div className=\"flex items-center justify-between\">\n        {/* Left side - Logo and Navigation */}\n        <div className=\"flex items-center space-x-8\">\n          {/* Logo */}\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"w-8 h-8 bg-primary rounded-full flex items-center justify-center\">\n              <span className=\"text-primary-foreground font-bold text-sm\">K</span>\n            </div>\n            <span className=\"text-xl font-bold text-foreground\">KickoffScore</span>\n          </div>\n\n          {/* Navigation Menu */}\n          <nav className=\"hidden md:flex items-center space-x-6\">\n            <a href=\"#\" className=\"text-muted-foreground hover:text-primary font-medium\">\n              News\n            </a>\n            <a href=\"#\" className=\"text-muted-foreground hover:text-primary font-medium\">\n              Transfers\n            </a>\n            <a href=\"#\" className=\"text-muted-foreground hover:text-primary font-medium\">\n              About us\n            </a>\n          </nav>\n        </div>\n\n        {/* Right side - Search and Settings */}\n        <div className=\"flex items-center space-x-4\">\n          {/* Search Bar */}\n          <div className=\"hidden md:flex items-center bg-muted rounded-lg px-3 py-2 w-64\">\n            <Search className=\"w-4 h-4 text-muted-foreground mr-2\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search teams, leagues, countries...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"bg-transparent outline-none text-sm flex-1 text-foreground placeholder-muted-foreground\"\n            />\n          </div>\n\n          {/* Settings */}\n          <div className=\"relative\" ref={settingsRef}>\n            <button\n              onClick={() => setIsSettingsOpen(!isSettingsOpen)}\n              className=\"p-2 text-muted-foreground hover:text-primary hover:bg-muted rounded-lg transition-colors\"\n            >\n              <Settings className=\"w-5 h-5\" />\n            </button>\n\n            {/* Settings Dropdown */}\n            {isSettingsOpen && (\n              <div className=\"absolute right-0 mt-2 w-64 bg-card rounded-lg shadow-lg border border-border py-2 z-50\">\n                {/* Sign In/Sign Up */}\n                <div className=\"px-4 py-2 border-b border-border\">\n                  <div className=\"flex items-center space-x-3\">\n                    <User className=\"w-4 h-4 text-muted-foreground\" />\n                    <div className=\"flex-1\">\n                      <button className=\"text-sm text-primary hover:text-primary/80 font-medium\">\n                        Sign In\n                      </button>\n                      <span className=\"text-sm text-muted-foreground mx-2\">|</span>\n                      <button className=\"text-sm text-primary hover:text-primary/80 font-medium\">\n                        Sign Up\n                      </button>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Theme Toggle */}\n                <div className=\"px-4 py-3\">\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-sm font-medium text-foreground\">Theme</span>\n                    <ThemeToggle />\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            onClick={onToggleSidebar}\n            className=\"md:hidden p-2 text-muted-foreground hover:text-primary hover:bg-muted rounded-lg transition-colors\"\n          >\n            <Menu className=\"w-5 h-5\" />\n          </button>\n        </div>\n      </div>\n\n      {/* Mobile Search Bar */}\n      <div className=\"md:hidden mt-3\">\n        <div className=\"flex items-center bg-muted rounded-lg px-3 py-2\">\n          <Search className=\"w-4 h-4 text-muted-foreground mr-2\" />\n          <input\n            type=\"text\"\n            placeholder=\"Search teams, leagues, countries...\"\n            value={searchQuery}\n            onChange={(e) => setSearchQuery(e.target.value)}\n            className=\"bg-transparent outline-none text-sm flex-1 text-foreground placeholder-muted-foreground\"\n          />\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;;;AAJA;;;;AAUe,SAAS,OAAO,KAAgC;QAAhC,EAAE,eAAe,EAAe,GAAhC;;IAC7B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;uDAAqB,CAAC;oBAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAC9E,kBAAkB;oBACpB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;oCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;2BAAG,EAAE;IAEL,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;kDAE9D,6LAAC;wCAAK,WAAU;kDAAoC;;;;;;;;;;;;0CAItD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,MAAK;wCAAI,WAAU;kDAAuD;;;;;;kDAG7E,6LAAC;wCAAE,MAAK;wCAAI,WAAU;kDAAuD;;;;;;kDAG7E,6LAAC;wCAAE,MAAK;wCAAI,WAAU;kDAAuD;;;;;;;;;;;;;;;;;;kCAOjF,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wCAC9C,WAAU;;;;;;;;;;;;0CAKd,6LAAC;gCAAI,WAAU;gCAAW,KAAK;;kDAC7B,6LAAC;wCACC,SAAS,IAAM,kBAAkB,CAAC;wCAClC,WAAU;kDAEV,cAAA,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;oCAIrB,gCACC,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAO,WAAU;8EAAyD;;;;;;8EAG3E,6LAAC;oEAAK,WAAU;8EAAqC;;;;;;8EACrD,6LAAC;oEAAO,WAAU;8EAAyD;;;;;;;;;;;;;;;;;;;;;;;0DAQjF,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAsC;;;;;;sEACtD,6LAAC,oIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQtB,6LAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAMtB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,yMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,6LAAC;4BACC,MAAK;4BACL,aAAY;4BACZ,OAAO;4BACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4BAC9C,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAMtB;GA5HwB;KAAA", "debugId": null}}, {"offset": {"line": 506, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/kickoffpredict/frontend/src/lib/api.ts"], "sourcesContent": ["import axios from 'axios';\n\n// Backend API configuration\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';\nconst API_KEY = process.env.NEXT_PUBLIC_API_KEY || '1e89f27ad4c94a5ba38fc7a96ee2427b9d3c582f6e804c3bb2e398bb8e231756';\n\n// Create axios instance with default config\nexport const api = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n    'X-API-Key': API_KEY,\n  },\n});\n\n// API response types\nexport interface League {\n  _id: number;\n  id?: number; // Optional for backward compatibility\n  league: {\n    id: number;\n    name: string;\n    type: string;\n    logo: string;\n  };\n  country: {\n    name: string;\n    code: string | null;\n    flag: string | null;\n  };\n  seasons: Array<{\n    year: number;\n    start: string;\n    end: string;\n    current: boolean;\n  }>;\n}\n\nexport interface Team {\n  id: number;\n  name: string;\n  logo: string;\n  country: string;\n}\n\nexport interface Fixture {\n  _id: number;\n  id?: number; // Optional for backward compatibility\n  fixture: {\n    id: number;\n    referee: string | null;\n    timezone: string;\n    date: string;\n    timestamp: number;\n    periods: {\n      first: number | null;\n      second: number | null;\n    };\n    venue: {\n      id: number | null;\n      name: string | null;\n      city: string | null;\n    };\n    status: {\n      long: string;\n      short: string;\n      elapsed: number | null;\n    };\n  };\n  league: {\n    id: number;\n    name: string;\n    country: string;\n    logo: string;\n    flag: string;\n    season: number;\n    round: string;\n  };\n  teams: {\n    home: Team;\n    away: Team;\n  };\n  goals: {\n    home: number | null;\n    away: number | null;\n  };\n  score: {\n    halftime: {\n      home: number | null;\n      away: number | null;\n    };\n    fulltime: {\n      home: number | null;\n      away: number | null;\n    };\n    extratime: {\n      home: number | null;\n      away: number | null;\n    };\n    penalty: {\n      home: number | null;\n      away: number | null;\n    };\n  };\n}\n\nexport interface Prediction {\n  fixture: {\n    id: number;\n  };\n  league: {\n    id: number;\n    name: string;\n  };\n  teams: {\n    home: Team;\n    away: Team;\n  };\n  predictions: {\n    winner: {\n      id: number | null;\n      name: string;\n      comment: string;\n    };\n    win_or_draw: boolean;\n    under_over: string | null;\n    goals: {\n      home: string;\n      away: string;\n    };\n    advice: string;\n    percent: {\n      home: string;\n      draw: string;\n      away: string;\n    };\n  };\n}\n\n// API functions\nexport const apiService = {\n  // Get leagues\n  async getLeagues(): Promise<League[]> {\n    try {\n      const response = await api.get('/api/leagues');\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching leagues:', error);\n      throw error;\n    }\n  },\n\n  // Get fixtures by date\n  async getFixtures(date?: string): Promise<Fixture[]> {\n    try {\n      const params = date ? { date } : {};\n      const response = await api.get('/api/fixtures', { params });\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching fixtures:', error);\n      throw error;\n    }\n  },\n\n  // Get fixtures by league\n  async getFixturesByLeague(leagueId: number, season?: number): Promise<Fixture[]> {\n    try {\n      const params = season ? { league: leagueId, season } : { league: leagueId };\n      const response = await api.get('/api/fixtures', { params });\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching fixtures by league:', error);\n      throw error;\n    }\n  },\n\n  // Get live fixtures\n  async getLiveFixtures(): Promise<Fixture[]> {\n    try {\n      const response = await api.get('/api/fixtures/live');\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching live fixtures:', error);\n      throw error;\n    }\n  },\n\n  // Get predictions\n  async getPredictions(fixtureId: number): Promise<Prediction> {\n    try {\n      const response = await api.get(`/api/predictions/${fixtureId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching predictions:', error);\n      throw error;\n    }\n  },\n\n  // Search fixtures\n  async searchFixtures(query: string): Promise<Fixture[]> {\n    try {\n      const response = await api.get('/api/fixtures/search', {\n        params: { q: query }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Error searching fixtures:', error);\n      throw error;\n    }\n  },\n\n  // Get team information\n  async getTeam(teamId: number): Promise<Team> {\n    try {\n      const response = await api.get(`/api/teams/${teamId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching team:', error);\n      throw error;\n    }\n  },\n\n  // Get league standings\n  async getStandings(leagueId: number, season?: number): Promise<any> {\n    try {\n      const params = season ? { league: leagueId, season } : { league: leagueId };\n      const response = await api.get('/api/standings', { params });\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching standings:', error);\n      throw error;\n    }\n  }\n};\n\n// Error handling utility\nexport const handleApiError = (error: any) => {\n  if (error.response) {\n    // Server responded with error status\n    console.error('API Error:', error.response.data);\n    return error.response.data.message || 'An error occurred';\n  } else if (error.request) {\n    // Request was made but no response received\n    console.error('Network Error:', error.request);\n    return 'Network error. Please check your connection.';\n  } else {\n    // Something else happened\n    console.error('Error:', error.message);\n    return error.message || 'An unexpected error occurred';\n  }\n};\n"], "names": [], "mappings": ";;;;;AAGqB;AAHrB;;AAEA,4BAA4B;AAC5B,MAAM,eAAe,6DAAmC;AACxD,MAAM,UAAU,wGAAmC;AAG5C,MAAM,MAAM,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC9B,SAAS;IACT,SAAS;QACP,gBAAgB;QAChB,aAAa;IACf;AACF;AA+HO,MAAM,aAAa;IACxB,cAAc;IACd,MAAM;QACJ,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;YAC/B,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM;QACR;IACF;IAEA,uBAAuB;IACvB,MAAM,aAAY,IAAa;QAC7B,IAAI;YACF,MAAM,SAAS,OAAO;gBAAE;YAAK,IAAI,CAAC;YAClC,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,iBAAiB;gBAAE;YAAO;YACzD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA,yBAAyB;IACzB,MAAM,qBAAoB,QAAgB,EAAE,MAAe;QACzD,IAAI;YACF,MAAM,SAAS,SAAS;gBAAE,QAAQ;gBAAU;YAAO,IAAI;gBAAE,QAAQ;YAAS;YAC1E,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,iBAAiB;gBAAE;YAAO;YACzD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA,oBAAoB;IACpB,MAAM;QACJ,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC;YAC/B,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA,kBAAkB;IAClB,MAAM,gBAAe,SAAiB;QACpC,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,AAAC,oBAA6B,OAAV;YACnD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,MAAM;QACR;IACF;IAEA,kBAAkB;IAClB,MAAM,gBAAe,KAAa;QAChC,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,wBAAwB;gBACrD,QAAQ;oBAAE,GAAG;gBAAM;YACrB;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,uBAAuB;IACvB,MAAM,SAAQ,MAAc;QAC1B,IAAI;YACF,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,AAAC,cAAoB,OAAP;YAC7C,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,MAAM;QACR;IACF;IAEA,uBAAuB;IACvB,MAAM,cAAa,QAAgB,EAAE,MAAe;QAClD,IAAI;YACF,MAAM,SAAS,SAAS;gBAAE,QAAQ;gBAAU;YAAO,IAAI;gBAAE,QAAQ;YAAS;YAC1E,MAAM,WAAW,MAAM,IAAI,GAAG,CAAC,kBAAkB;gBAAE;YAAO;YAC1D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;AACF;AAGO,MAAM,iBAAiB,CAAC;IAC7B,IAAI,MAAM,QAAQ,EAAE;QAClB,qCAAqC;QACrC,QAAQ,KAAK,CAAC,cAAc,MAAM,QAAQ,CAAC,IAAI;QAC/C,OAAO,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI;IACxC,OAAO,IAAI,MAAM,OAAO,EAAE;QACxB,4CAA4C;QAC5C,QAAQ,KAAK,CAAC,kBAAkB,MAAM,OAAO;QAC7C,OAAO;IACT,OAAO;QACL,0BAA0B;QAC1B,QAAQ,KAAK,CAAC,UAAU,MAAM,OAAO;QACrC,OAAO,MAAM,OAAO,IAAI;IAC1B;AACF", "debugId": null}}, {"offset": {"line": 655, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/kickoffpredict/frontend/src/components/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { ChevronDown, ChevronRight, Filter } from 'lucide-react';\nimport { apiService, League, handleApiError } from '@/lib/api';\n\ninterface SidebarProps {\n  onLeagueSelect?: (leagueId: number) => void;\n  selectedLeagueId?: number | null;\n}\n\ninterface CountryGroup {\n  name: string;\n  flag: string | null;\n  leagues: League[];\n}\n\nexport default function Sidebar({ onLeagueSelect, selectedLeagueId }: SidebarProps) {\n  const [allLeaguesOpen, setAllLeaguesOpen] = useState(false);\n  const [topLeagues, setTopLeagues] = useState<League[]>([]);\n  const [allLeagues, setAllLeagues] = useState<League[]>([]);\n  const [isLoadingTopLeagues, setIsLoadingTopLeagues] = useState(true);\n  const [isLoadingAllLeagues, setIsLoadingAllLeagues] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [expandedCountries, setExpandedCountries] = useState<Set<string>>(new Set());\n  const [searchQuery, setSearchQuery] = useState('');\n\n  useEffect(() => {\n    const fetchLeagues = async () => {\n      setError(null);\n      setIsLoadingTopLeagues(true);\n      try {\n        const leaguesData = await apiService.getLeagues();\n        const topLeagueIds = [39, 140, 78, 135, 61, 2, 3];\n        setTopLeagues(leaguesData.filter(l => topLeagueIds.includes(l._id)));\n        setAllLeagues(leaguesData.filter(l => !topLeagueIds.includes(l._id)));\n        setIsLoadingTopLeagues(false);\n      } catch (err) {\n        setError(handleApiError(err));\n        setIsLoadingTopLeagues(false);\n        console.error('Failed to fetch leagues:', err);\n      }\n    };\n    fetchLeagues();\n  }, []);\n\n  const groupLeaguesByCountry = (leaguesList: League[]): CountryGroup[] => {\n    const map = new Map<string, CountryGroup>();\n    leaguesList.forEach(league => {\n      const name = league.country.name;\n      if (!map.has(name)) {\n        map.set(name, {\n          name,\n          flag: league.country.flag,\n          leagues: [],\n        });\n      }\n      map.get(name)!.leagues.push(league);\n    });\n    return Array.from(map.values())\n      .sort((a, b) => a.name.localeCompare(b.name))\n      .map(group => ({\n        ...group,\n        leagues: group.leagues.sort((a, b) => a.league.name.localeCompare(b.league.name)),\n      }));\n  };\n\n  const filteredAllLeagues = allLeagues.filter(l =>\n    l.league.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n    l.country.name.toLowerCase().includes(searchQuery.toLowerCase())\n  );\n\n  const countryGroups = groupLeaguesByCountry(filteredAllLeagues);\n\n  const toggleCountry = (name: string) => {\n    const updated = new Set(expandedCountries);\n    updated.has(name) ? updated.delete(name) : updated.add(name);\n    setExpandedCountries(updated);\n  };\n\n  const LeagueItemSkeleton = () => (\n    <div className=\"flex items-center space-x-3 p-2 w-full hover:bg-muted rounded-lg transition-colors\">\n      <div className=\"w-6 h-6 bg-muted rounded animate-pulse\" />\n      <div className=\"flex-1 min-w-0\">\n        <div className=\"h-4 bg-muted rounded animate-pulse w-full mb-1\" />\n        <div className=\"h-3 bg-muted rounded animate-pulse w-3/4\" />\n      </div>\n    </div>\n  );\n\n  const CountryItemSkeleton = () => (\n    <div className=\"rounded-lg overflow-hidden w-full\">\n      <div className=\"w-full flex items-center justify-between p-3 hover:bg-muted transition-colors\">\n        <div className=\"flex items-center space-x-3 flex-1 min-w-0\">\n          <div className=\"w-6 h-4 bg-muted rounded-sm animate-pulse\" />\n          <div className=\"h-4 bg-muted rounded animate-pulse flex-1 max-w-32\" />\n        </div>\n        <div className=\"w-4 h-4 bg-muted rounded animate-pulse ml-3\" />\n      </div>\n    </div>\n  );\n\n  const LeagueItem = ({ league }: { league: League }) => (\n    <div className=\"flex items-center space-x-3 p-2 w-full hover:bg-muted rounded-lg transition-colors\">\n      <div className=\"w-6 h-6 bg-muted rounded flex items-center justify-center overflow-hidden\">\n        <img\n          src={league.league.logo}\n          alt={league.league.name}\n          className=\"w-6 h-6 object-contain\"\n          onError={(e) => {\n            e.currentTarget.style.display = 'none';\n            const parent = e.currentTarget.parentElement;\n            if (parent) {\n              parent.innerHTML =\n                '<span class=\"text-xs font-bold text-muted-foreground\">' +\n                league.league.name.charAt(0) +\n                '</span>';\n            }\n          }}\n        />\n      </div>\n      <div className=\"flex-1 min-w-0\">\n        <p className=\"text-sm font-medium truncate text-foreground\">\n          {league.league.name}\n        </p>\n        <p className=\"text-xs truncate text-muted-foreground\">{league.country.name}</p>\n      </div>\n    </div>\n  );\n\n  return (\n    <div\n      className=\"pl-4 pr-2 py-4 space-y-4 h-full overflow-y-auto\"\n      style={{ width: '251.68px', minWidth: '251.68px' }}\n      suppressHydrationWarning\n    >\n      {/* Top Leagues */}\n      <div\n        className=\"bg-card rounded-lg border border-border overflow-hidden\"\n        style={{ height: '464px' }}\n      >\n        <div className=\"p-4 h-full overflow-y-auto\">\n          <h3 className=\"text-lg font-semibold text-foreground mb-4\">Top Leagues</h3>\n          <div className=\"space-y-1\">\n            {isLoadingTopLeagues\n              ? Array.from({ length: 7 }).map((_, i) => <LeagueItemSkeleton key={i} />)\n              : topLeagues.length > 0\n              ? topLeagues.map(league => (\n                  <LeagueItem key={league._id} league={league} />\n                ))\n              : error\n              ? <div className=\"text-destructive text-sm py-2\">Failed to load leagues</div>\n              : <div className=\"text-muted-foreground text-sm py-2\">No leagues available</div>}\n          </div>\n        </div>\n      </div>\n\n      {/* All Leagues */}\n      <div className=\"bg-card rounded-lg border border-border overflow-hidden\">\n        <button\n          onClick={() => {\n            if (!allLeaguesOpen && allLeagues.length === 0 && !isLoadingAllLeagues) {\n              setIsLoadingAllLeagues(true);\n              setTimeout(() => setIsLoadingAllLeagues(false), 800);\n            }\n            setAllLeaguesOpen(!allLeaguesOpen);\n          }}\n          className=\"w-full flex items-center justify-between p-4 hover:bg-muted transition-colors\"\n        >\n          <h3 className=\"text-lg font-semibold text-foreground\">All leagues</h3>\n          {allLeaguesOpen\n            ? <ChevronDown className=\"w-5 h-5 text-muted-foreground\" />\n            : <ChevronRight className=\"w-5 h-5 text-muted-foreground\" />}\n        </button>\n\n        {allLeaguesOpen && (\n          <div className=\"px-4 pb-4\">\n            <div className=\"mb-4\">\n              <div className=\"flex items-center space-x-2 p-3 border border-border rounded-lg bg-muted\">\n                <Filter className=\"w-4 h-4 text-muted-foreground\" />\n                <input\n                  type=\"text\"\n                  placeholder=\"Filter\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"flex-1 bg-transparent text-sm text-foreground placeholder-muted-foreground outline-none\"\n                />\n              </div>\n            </div>\n\n            <div className=\"space-y-2\">\n              {isLoadingAllLeagues\n                ? Array.from({ length: 3 }).map((_, i) => <CountryItemSkeleton key={i} />)\n                : countryGroups.length > 0\n                ? countryGroups.map(country => (\n                    <div key={country.name} className=\"rounded-lg overflow-hidden w-full\">\n                      <button\n                        onClick={() => toggleCountry(country.name)}\n                        className=\"w-full flex items-center justify-between p-3 hover:bg-muted transition-colors\"\n                      >\n                        <div className=\"flex items-center space-x-3\">\n                          <div className=\"w-6 h-6 flex items-center justify-center\">\n                            {country.flag ? (\n                              <img\n                                src={country.flag}\n                                alt={country.name}\n                                className=\"w-6 h-4 object-cover rounded-sm\"\n                                onError={(e) => (e.currentTarget.style.display = 'none')}\n                              />\n                            ) : (\n                              <div className=\"w-6 h-4 bg-muted rounded-sm\" />\n                            )}\n                          </div>\n                          <span className=\"text-sm font-medium text-foreground\">{country.name}</span>\n                        </div>\n                        <ChevronDown\n                          className={`w-4 h-4 text-muted-foreground transition-transform ${\n                            expandedCountries.has(country.name) ? 'rotate-180' : ''\n                          }`}\n                        />\n                      </button>\n                      {expandedCountries.has(country.name) && (\n                        <div className=\"px-3 pb-3 space-y-1\">\n                          {country.leagues.map(league => (\n                            <LeagueItem key={league._id} league={league} />\n                          ))}\n                        </div>\n                      )}\n                    </div>\n                  ))\n                : <div className=\"text-muted-foreground text-sm py-2\">\n                    {searchQuery ? `No leagues found matching \"${searchQuery}\"` : 'No additional leagues available'}\n                  </div>}\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;;;AAJA;;;;AAiBe,SAAS,QAAQ,KAAkD;QAAlD,EAAE,cAAc,EAAE,gBAAgB,EAAgB,GAAlD;;IAC9B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACzD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAC5E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6BAAE;YACR,MAAM;kDAAe;oBACnB,SAAS;oBACT,uBAAuB;oBACvB,IAAI;wBACF,MAAM,cAAc,MAAM,oHAAA,CAAA,aAAU,CAAC,UAAU;wBAC/C,MAAM,eAAe;4BAAC;4BAAI;4BAAK;4BAAI;4BAAK;4BAAI;4BAAG;yBAAE;wBACjD,cAAc,YAAY,MAAM;8DAAC,CAAA,IAAK,aAAa,QAAQ,CAAC,EAAE,GAAG;;wBACjE,cAAc,YAAY,MAAM;8DAAC,CAAA,IAAK,CAAC,aAAa,QAAQ,CAAC,EAAE,GAAG;;wBAClE,uBAAuB;oBACzB,EAAE,OAAO,KAAK;wBACZ,SAAS,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE;wBACxB,uBAAuB;wBACvB,QAAQ,KAAK,CAAC,4BAA4B;oBAC5C;gBACF;;YACA;QACF;4BAAG,EAAE;IAEL,MAAM,wBAAwB,CAAC;QAC7B,MAAM,MAAM,IAAI;QAChB,YAAY,OAAO,CAAC,CAAA;YAClB,MAAM,OAAO,OAAO,OAAO,CAAC,IAAI;YAChC,IAAI,CAAC,IAAI,GAAG,CAAC,OAAO;gBAClB,IAAI,GAAG,CAAC,MAAM;oBACZ;oBACA,MAAM,OAAO,OAAO,CAAC,IAAI;oBACzB,SAAS,EAAE;gBACb;YACF;YACA,IAAI,GAAG,CAAC,MAAO,OAAO,CAAC,IAAI,CAAC;QAC9B;QACA,OAAO,MAAM,IAAI,CAAC,IAAI,MAAM,IACzB,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,IAAI,CAAC,aAAa,CAAC,EAAE,IAAI,GAC1C,GAAG,CAAC,CAAA,QAAS,CAAC;gBACb,GAAG,KAAK;gBACR,SAAS,MAAM,OAAO,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,MAAM,CAAC,IAAI;YACjF,CAAC;IACL;IAEA,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAA,IAC3C,EAAE,MAAM,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC5D,EAAE,OAAO,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;IAG/D,MAAM,gBAAgB,sBAAsB;IAE5C,MAAM,gBAAgB,CAAC;QACrB,MAAM,UAAU,IAAI,IAAI;QACxB,QAAQ,GAAG,CAAC,QAAQ,QAAQ,MAAM,CAAC,QAAQ,QAAQ,GAAG,CAAC;QACvD,qBAAqB;IACvB;IAEA,MAAM,qBAAqB,kBACzB,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BACf,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;IAKrB,MAAM,sBAAsB,kBAC1B,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAEjB,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAKrB,MAAM,aAAa;YAAC,EAAE,MAAM,EAAsB;6BAChD,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,KAAK,OAAO,MAAM,CAAC,IAAI;wBACvB,KAAK,OAAO,MAAM,CAAC,IAAI;wBACvB,WAAU;wBACV,SAAS,CAAC;4BACR,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;4BAChC,MAAM,SAAS,EAAE,aAAa,CAAC,aAAa;4BAC5C,IAAI,QAAQ;gCACV,OAAO,SAAS,GACd,2DACA,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAC1B;4BACJ;wBACF;;;;;;;;;;;8BAGJ,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCACV,OAAO,MAAM,CAAC,IAAI;;;;;;sCAErB,6LAAC;4BAAE,WAAU;sCAA0C,OAAO,OAAO,CAAC,IAAI;;;;;;;;;;;;;;;;;;;IAKhF,qBACE,6LAAC;QACC,WAAU;QACV,OAAO;YAAE,OAAO;YAAY,UAAU;QAAW;QACjD,wBAAwB;;0BAGxB,6LAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,QAAQ;gBAAQ;0BAEzB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA6C;;;;;;sCAC3D,6LAAC;4BAAI,WAAU;sCACZ,sBACG,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBAAM,6LAAC,wBAAwB;;;;4CACjE,WAAW,MAAM,GAAG,IACpB,WAAW,GAAG,CAAC,CAAA,uBACb,6LAAC;oCAA4B,QAAQ;mCAApB,OAAO,GAAG;;;;4CAE7B,sBACA,6LAAC;gCAAI,WAAU;0CAAgC;;;;;qDAC/C,6LAAC;gCAAI,WAAU;0CAAqC;;;;;;;;;;;;;;;;;;;;;;0BAM9D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS;4BACP,IAAI,CAAC,kBAAkB,WAAW,MAAM,KAAK,KAAK,CAAC,qBAAqB;gCACtE,uBAAuB;gCACvB,WAAW,IAAM,uBAAuB,QAAQ;4BAClD;4BACA,kBAAkB,CAAC;wBACrB;wBACA,WAAU;;0CAEV,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;4BACrD,+BACG,6LAAC,uNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDACvB,6LAAC,yNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;;oBAG7B,gCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;4CACC,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,WAAU;;;;;;;;;;;;;;;;;0CAKhB,6LAAC;gCAAI,WAAU;0CACZ,sBACG,MAAM,IAAI,CAAC;oCAAE,QAAQ;gCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBAAM,6LAAC,yBAAyB;;;;gDAClE,cAAc,MAAM,GAAG,IACvB,cAAc,GAAG,CAAC,CAAA,wBAChB,6LAAC;wCAAuB,WAAU;;0DAChC,6LAAC;gDACC,SAAS,IAAM,cAAc,QAAQ,IAAI;gDACzC,WAAU;;kEAEV,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACZ,QAAQ,IAAI,iBACX,6LAAC;oEACC,KAAK,QAAQ,IAAI;oEACjB,KAAK,QAAQ,IAAI;oEACjB,WAAU;oEACV,SAAS,CAAC,IAAO,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;;;;;yFAGnD,6LAAC;oEAAI,WAAU;;;;;;;;;;;0EAGnB,6LAAC;gEAAK,WAAU;0EAAuC,QAAQ,IAAI;;;;;;;;;;;;kEAErE,6LAAC,uNAAA,CAAA,cAAW;wDACV,WAAW,AAAC,sDAEX,OADC,kBAAkB,GAAG,CAAC,QAAQ,IAAI,IAAI,eAAe;;;;;;;;;;;;4CAI1D,kBAAkB,GAAG,CAAC,QAAQ,IAAI,mBACjC,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,OAAO,CAAC,GAAG,CAAC,CAAA,uBACnB,6LAAC;wDAA4B,QAAQ;uDAApB,OAAO,GAAG;;;;;;;;;;;uCA7BzB,QAAQ,IAAI;;;;8DAmCxB,6LAAC;oCAAI,WAAU;8CACZ,cAAc,AAAC,8BAAyC,OAAZ,aAAY,OAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlF;GA9NwB;KAAA", "debugId": null}}, {"offset": {"line": 1163, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/kickoffpredict/frontend/src/hooks/useSocket.ts"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef, useState } from 'react';\nimport { io, Socket } from 'socket.io-client';\nimport { Fixture } from '@/lib/api';\n\ninterface UseSocketOptions {\n  namespace?: string;\n  autoConnect?: boolean;\n}\n\ninterface SocketEvents {\n  'fixture-update': (fixture: Fixture) => void;\n  'fixture-goal': (data: { fixtureId: number; team: 'home' | 'away'; player: string; minute: number }) => void;\n  'fixture-card': (data: { fixtureId: number; team: 'home' | 'away'; player: string; card: 'yellow' | 'red'; minute: number }) => void;\n  'fixture-status': (data: { fixtureId: number; status: string; elapsed?: number }) => void;\n  'live-fixtures': (fixtures: Fixture[]) => void;\n}\n\nexport function useSocket(options: UseSocketOptions = {}) {\n  const { namespace = '/fixtures', autoConnect = true } = options;\n  const [socket, setSocket] = useState<Socket | null>(null);\n  const [connected, setConnected] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const socketRef = useRef<Socket | null>(null);\n\n  useEffect(() => {\n    if (!autoConnect) return;\n\n    const SOCKET_URL = process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3000';\n    \n    // Create socket connection\n    const newSocket = io(`${SOCKET_URL}${namespace}`, {\n      transports: ['websocket', 'polling'],\n      timeout: 20000,\n      forceNew: true,\n    });\n\n    socketRef.current = newSocket;\n    setSocket(newSocket);\n\n    // Connection event handlers\n    newSocket.on('connect', () => {\n      console.log(`Connected to ${namespace} namespace`);\n      setConnected(true);\n      setError(null);\n    });\n\n    newSocket.on('disconnect', (reason) => {\n      console.log(`Disconnected from ${namespace} namespace:`, reason);\n      setConnected(false);\n    });\n\n    newSocket.on('connect_error', (err) => {\n      console.error(`Connection error to ${namespace} namespace:`, err);\n      setError(err.message);\n      setConnected(false);\n    });\n\n    // Cleanup on unmount\n    return () => {\n      if (newSocket) {\n        newSocket.disconnect();\n      }\n    };\n  }, [namespace, autoConnect]);\n\n  // Subscribe to fixture updates\n  const subscribeToFixture = (fixtureId: number) => {\n    if (socket && connected) {\n      socket.emit('subscribe-fixture', fixtureId);\n      console.log(`Subscribed to fixture ${fixtureId}`);\n    }\n  };\n\n  // Unsubscribe from fixture updates\n  const unsubscribeFromFixture = (fixtureId: number) => {\n    if (socket && connected) {\n      socket.emit('unsubscribe-fixture', fixtureId);\n      console.log(`Unsubscribed from fixture ${fixtureId}`);\n    }\n  };\n\n  // Subscribe to league updates\n  const subscribeToLeague = (leagueId: number) => {\n    if (socket && connected) {\n      socket.emit('subscribe-league', leagueId);\n      console.log(`Subscribed to league ${leagueId}`);\n    }\n  };\n\n  // Unsubscribe from league updates\n  const unsubscribeFromLeague = (leagueId: number) => {\n    if (socket && connected) {\n      socket.emit('unsubscribe-league', leagueId);\n      console.log(`Unsubscribed from league ${leagueId}`);\n    }\n  };\n\n  // Subscribe to live fixtures\n  const subscribeToLiveFixtures = () => {\n    if (socket && connected) {\n      socket.emit('subscribe-live');\n      console.log('Subscribed to live fixtures');\n    }\n  };\n\n  // Unsubscribe from live fixtures\n  const unsubscribeFromLiveFixtures = () => {\n    if (socket && connected) {\n      socket.emit('unsubscribe-live');\n      console.log('Unsubscribed from live fixtures');\n    }\n  };\n\n  // Generic event listener\n  const on = <K extends keyof SocketEvents>(event: K, callback: SocketEvents[K]) => {\n    if (socket) {\n      socket.on(event, callback);\n    }\n  };\n\n  // Remove event listener\n  const off = <K extends keyof SocketEvents>(event: K, callback?: SocketEvents[K]) => {\n    if (socket) {\n      if (callback) {\n        socket.off(event, callback);\n      } else {\n        socket.off(event);\n      }\n    }\n  };\n\n  // Emit event\n  const emit = (event: string, data?: any) => {\n    if (socket && connected) {\n      socket.emit(event, data);\n    }\n  };\n\n  return {\n    socket,\n    connected,\n    error,\n    subscribeToFixture,\n    unsubscribeFromFixture,\n    subscribeToLeague,\n    unsubscribeFromLeague,\n    subscribeToLiveFixtures,\n    unsubscribeFromLiveFixtures,\n    on,\n    off,\n    emit,\n  };\n}\n\n// Hook for fixture-specific updates\nexport function useFixtureSocket(fixtureId: number | null) {\n  const { \n    connected, \n    subscribeToFixture, \n    unsubscribeFromFixture, \n    on, \n    off \n  } = useSocket();\n  \n  const [fixture, setFixture] = useState<Fixture | null>(null);\n  const [events, setEvents] = useState<any[]>([]);\n\n  useEffect(() => {\n    if (!fixtureId || !connected) return;\n\n    // Subscribe to fixture updates\n    subscribeToFixture(fixtureId);\n\n    // Set up event listeners\n    const handleFixtureUpdate = (updatedFixture: Fixture) => {\n      if (updatedFixture.id === fixtureId) {\n        setFixture(updatedFixture);\n      }\n    };\n\n    const handleGoal = (data: any) => {\n      if (data.fixtureId === fixtureId) {\n        setEvents(prev => [...prev, { type: 'goal', ...data, timestamp: Date.now() }]);\n      }\n    };\n\n    const handleCard = (data: any) => {\n      if (data.fixtureId === fixtureId) {\n        setEvents(prev => [...prev, { type: 'card', ...data, timestamp: Date.now() }]);\n      }\n    };\n\n    const handleStatus = (data: any) => {\n      if (data.fixtureId === fixtureId) {\n        setEvents(prev => [...prev, { type: 'status', ...data, timestamp: Date.now() }]);\n      }\n    };\n\n    on('fixture-update', handleFixtureUpdate);\n    on('fixture-goal', handleGoal);\n    on('fixture-card', handleCard);\n    on('fixture-status', handleStatus);\n\n    // Cleanup\n    return () => {\n      unsubscribeFromFixture(fixtureId);\n      off('fixture-update', handleFixtureUpdate);\n      off('fixture-goal', handleGoal);\n      off('fixture-card', handleCard);\n      off('fixture-status', handleStatus);\n    };\n  }, [fixtureId, connected, subscribeToFixture, unsubscribeFromFixture, on, off]);\n\n  return {\n    fixture,\n    events,\n    connected,\n  };\n}\n\n// Hook for live fixtures\nexport function useLiveFixtures() {\n  const { \n    connected, \n    subscribeToLiveFixtures, \n    unsubscribeFromLiveFixtures, \n    on, \n    off \n  } = useSocket();\n  \n  const [liveFixtures, setLiveFixtures] = useState<Fixture[]>([]);\n\n  useEffect(() => {\n    if (!connected) return;\n\n    // Subscribe to live fixtures\n    subscribeToLiveFixtures();\n\n    // Set up event listener\n    const handleLiveFixtures = (fixtures: Fixture[]) => {\n      setLiveFixtures(fixtures);\n    };\n\n    on('live-fixtures', handleLiveFixtures);\n\n    // Cleanup\n    return () => {\n      unsubscribeFromLiveFixtures();\n      off('live-fixtures', handleLiveFixtures);\n    };\n  }, [connected, subscribeToLiveFixtures, unsubscribeFromLiveFixtures, on, off]);\n\n  return {\n    liveFixtures,\n    connected,\n  };\n}\n"], "names": [], "mappings": ";;;;;AA6BuB;AA3BvB;AACA;AAAA;;AAHA;;;AAmBO,SAAS;QAAU,UAAA,iEAA4B,CAAC;;IACrD,MAAM,EAAE,YAAY,WAAW,EAAE,cAAc,IAAI,EAAE,GAAG;IACxD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAiB;IAExC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,CAAC,aAAa;YAElB,MAAM,aAAa,6DAAsC;YAEzD,2BAA2B;YAC3B,MAAM,YAAY,CAAA,GAAA,kLAAA,CAAA,KAAE,AAAD,EAAE,AAAC,GAAe,OAAb,YAAuB,OAAV,YAAa;gBAChD,YAAY;oBAAC;oBAAa;iBAAU;gBACpC,SAAS;gBACT,UAAU;YACZ;YAEA,UAAU,OAAO,GAAG;YACpB,UAAU;YAEV,4BAA4B;YAC5B,UAAU,EAAE,CAAC;uCAAW;oBACtB,QAAQ,GAAG,CAAC,AAAC,gBAAyB,OAAV,WAAU;oBACtC,aAAa;oBACb,SAAS;gBACX;;YAEA,UAAU,EAAE,CAAC;uCAAc,CAAC;oBAC1B,QAAQ,GAAG,CAAC,AAAC,qBAA8B,OAAV,WAAU,gBAAc;oBACzD,aAAa;gBACf;;YAEA,UAAU,EAAE,CAAC;uCAAiB,CAAC;oBAC7B,QAAQ,KAAK,CAAC,AAAC,uBAAgC,OAAV,WAAU,gBAAc;oBAC7D,SAAS,IAAI,OAAO;oBACpB,aAAa;gBACf;;YAEA,qBAAqB;YACrB;uCAAO;oBACL,IAAI,WAAW;wBACb,UAAU,UAAU;oBACtB;gBACF;;QACF;8BAAG;QAAC;QAAW;KAAY;IAE3B,+BAA+B;IAC/B,MAAM,qBAAqB,CAAC;QAC1B,IAAI,UAAU,WAAW;YACvB,OAAO,IAAI,CAAC,qBAAqB;YACjC,QAAQ,GAAG,CAAC,AAAC,yBAAkC,OAAV;QACvC;IACF;IAEA,mCAAmC;IACnC,MAAM,yBAAyB,CAAC;QAC9B,IAAI,UAAU,WAAW;YACvB,OAAO,IAAI,CAAC,uBAAuB;YACnC,QAAQ,GAAG,CAAC,AAAC,6BAAsC,OAAV;QAC3C;IACF;IAEA,8BAA8B;IAC9B,MAAM,oBAAoB,CAAC;QACzB,IAAI,UAAU,WAAW;YACvB,OAAO,IAAI,CAAC,oBAAoB;YAChC,QAAQ,GAAG,CAAC,AAAC,wBAAgC,OAAT;QACtC;IACF;IAEA,kCAAkC;IAClC,MAAM,wBAAwB,CAAC;QAC7B,IAAI,UAAU,WAAW;YACvB,OAAO,IAAI,CAAC,sBAAsB;YAClC,QAAQ,GAAG,CAAC,AAAC,4BAAoC,OAAT;QAC1C;IACF;IAEA,6BAA6B;IAC7B,MAAM,0BAA0B;QAC9B,IAAI,UAAU,WAAW;YACvB,OAAO,IAAI,CAAC;YACZ,QAAQ,GAAG,CAAC;QACd;IACF;IAEA,iCAAiC;IACjC,MAAM,8BAA8B;QAClC,IAAI,UAAU,WAAW;YACvB,OAAO,IAAI,CAAC;YACZ,QAAQ,GAAG,CAAC;QACd;IACF;IAEA,yBAAyB;IACzB,MAAM,KAAK,CAA+B,OAAU;QAClD,IAAI,QAAQ;YACV,OAAO,EAAE,CAAC,OAAO;QACnB;IACF;IAEA,wBAAwB;IACxB,MAAM,MAAM,CAA+B,OAAU;QACnD,IAAI,QAAQ;YACV,IAAI,UAAU;gBACZ,OAAO,GAAG,CAAC,OAAO;YACpB,OAAO;gBACL,OAAO,GAAG,CAAC;YACb;QACF;IACF;IAEA,aAAa;IACb,MAAM,OAAO,CAAC,OAAe;QAC3B,IAAI,UAAU,WAAW;YACvB,OAAO,IAAI,CAAC,OAAO;QACrB;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAvIgB;AA0IT,SAAS,iBAAiB,SAAwB;;IACvD,MAAM,EACJ,SAAS,EACT,kBAAkB,EAClB,sBAAsB,EACtB,EAAE,EACF,GAAG,EACJ,GAAG;IAEJ,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAE9C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,IAAI,CAAC,aAAa,CAAC,WAAW;YAE9B,+BAA+B;YAC/B,mBAAmB;YAEnB,yBAAyB;YACzB,MAAM;kEAAsB,CAAC;oBAC3B,IAAI,eAAe,EAAE,KAAK,WAAW;wBACnC,WAAW;oBACb;gBACF;;YAEA,MAAM;yDAAa,CAAC;oBAClB,IAAI,KAAK,SAAS,KAAK,WAAW;wBAChC;qEAAU,CAAA,OAAQ;uCAAI;oCAAM;wCAAE,MAAM;wCAAQ,GAAG,IAAI;wCAAE,WAAW,KAAK,GAAG;oCAAG;iCAAE;;oBAC/E;gBACF;;YAEA,MAAM;yDAAa,CAAC;oBAClB,IAAI,KAAK,SAAS,KAAK,WAAW;wBAChC;qEAAU,CAAA,OAAQ;uCAAI;oCAAM;wCAAE,MAAM;wCAAQ,GAAG,IAAI;wCAAE,WAAW,KAAK,GAAG;oCAAG;iCAAE;;oBAC/E;gBACF;;YAEA,MAAM;2DAAe,CAAC;oBACpB,IAAI,KAAK,SAAS,KAAK,WAAW;wBAChC;uEAAU,CAAA,OAAQ;uCAAI;oCAAM;wCAAE,MAAM;wCAAU,GAAG,IAAI;wCAAE,WAAW,KAAK,GAAG;oCAAG;iCAAE;;oBACjF;gBACF;;YAEA,GAAG,kBAAkB;YACrB,GAAG,gBAAgB;YACnB,GAAG,gBAAgB;YACnB,GAAG,kBAAkB;YAErB,UAAU;YACV;8CAAO;oBACL,uBAAuB;oBACvB,IAAI,kBAAkB;oBACtB,IAAI,gBAAgB;oBACpB,IAAI,gBAAgB;oBACpB,IAAI,kBAAkB;gBACxB;;QACF;qCAAG;QAAC;QAAW;QAAW;QAAoB;QAAwB;QAAI;KAAI;IAE9E,OAAO;QACL;QACA;QACA;IACF;AACF;IA/DgB;;QAOV;;;AA2DC,SAAS;;IACd,MAAM,EACJ,SAAS,EACT,uBAAuB,EACvB,2BAA2B,EAC3B,EAAE,EACF,GAAG,EACJ,GAAG;IAEJ,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IAE9D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,CAAC,WAAW;YAEhB,6BAA6B;YAC7B;YAEA,wBAAwB;YACxB,MAAM;gEAAqB,CAAC;oBAC1B,gBAAgB;gBAClB;;YAEA,GAAG,iBAAiB;YAEpB,UAAU;YACV;6CAAO;oBACL;oBACA,IAAI,iBAAiB;gBACvB;;QACF;oCAAG;QAAC;QAAW;QAAyB;QAA6B;QAAI;KAAI;IAE7E,OAAO;QACL;QACA;IACF;AACF;IAnCgB;;QAOV", "debugId": null}}, {"offset": {"line": 1463, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/kickoffpredict/frontend/src/lib/leagueTiers.ts"], "sourcesContent": ["/**\n * Configuration file for league tiers\n * This file contains the mapping of league IDs to their popularity tiers\n * Tier 1 is the most popular, Tier 6 is the least popular\n */\n\n// League tier mapping\nexport const leagueTierMap: Record<number, number> = {\n  // Tier 1: Pinnacle Global & Continental Competitions\n  1: 1, // World Cup (FIFA)\n  8: 1, // World Cup (FIFA)\n  4: 1, // European Championship (UEFA Euro) / European Championship Qualification\n  5: 1, // UEFA Nations League\n  2: 1, // Champions League (UEFA)\n  3: 1, // Europa League (UEFA)\n\n  // Tier 2: The \"Big 5\" European Leagues\n  39: 2, // Premier League (England)\n  140: 2, // La Liga (Spain)\n  78: 2, // Bundesliga (Germany)\n  135: 2, // Serie A (Italy)\n  61: 2, // Ligue 1 (France)\n  9: 2, // Copa America (CONMEBOL)\n  13: 2, // Copa Libertadores (CONMEBOL)\n  6: 2, // Africa Cup of Nations (CAF)\n  7: 2, // AFC Asian Cup (Asia)\n\n  // Tier 3: Major Domestic Cups, Other Strong Continental & Key Leagues\n  45: 3, // FA Cup (England)\n  143: 3, // Copa Del Rey (Spain)\n  81: 3, // DFB Pokal (Germany)\n  137: 3, // Coppa Italia (Italy)\n  848: 3, // Europa Conference League (UEFA)\n  66: 3, // Coupe de France (France)\n  48: 3, // League Cup (Carabao Cup) (England)\n  71: 3, // Serie A (Brazil)\n  128: 3, // Liga Profesional de Fútbol (Argentina)\n  253: 3, // Major League Soccer (USA)\n  307: 3, // Pro League (Saudi Arabia)\n  88: 3, // Eredivisie (Netherlands - includes Eredivisie Playoffs)\n  94: 3, // Liga Portugal (Primeira Liga) (Portugal)\n  203: 3, // Super Lig (Süper Lig) (Turkey)\n  15: 3, // FIFA Club World Cup\n  32: 3, // WC Qualification Europe (UEFA)\n  34: 3, // WC Qualification South America (CONMEBOL)\n  30: 3, // WC Qualification Asia (AFC)\n  29: 3, // WC Qualification Africa (CAF)\n  31: 3, // WC Qualification Concacaf\n  36: 3, // Africa Cup of Nations Qualifications (CAF)\n  38: 3, // UEFA U21 Championship\n  850: 3, // UEFA U21 Championship - Qualification\n\n  // Tier 4: Strong Second Tiers, Other Notable Leagues & Cups\n  40: 4, // Championship (England - includes Championship Play-Offs)\n  79: 4, // 2. Bundesliga (Germany)\n  141: 4, // La Liga 2 (Segunda División) (Spain)\n  136: 4, // Serie B (Italy)\n  62: 4, // Ligue 2 (France)\n  667: 4, // Friendlies Clubs (World)\n  10: 4, // Friendlies International (World)\n  188: 4, // A-League Men (Australia)\n  262: 4, // Liga MX (Mexico)\n  17: 4, // AFC Champions League Elite (Asia)\n  12: 4, // CAF Champions League (Africa)\n  856: 4, // CONCACAF Champions Cup (North/Central America & Caribbean)\n  11: 4, // Copa Sudamericana (CONMEBOL)\n  73: 4, // Copa do Brasil (Brazil)\n  130: 4, // Copa Argentina (Argentina)\n  144: 4, // Pro League (Jupiler Pro League) (Belgium)\n  179: 4, // Premiership Play-Offs (Scotland) - Represents the top flight implicitly\n  408: 4, // Premiership (Northern Ireland)\n  181: 4, // Scottish Cup (FA Cup) (Scotland)\n  90: 4, // KNVB Beker (Netherlands)\n  96: 4, // Taça De Portugal (Portugal)\n  206: 4, // Turkish Cup (Turkey)\n  218: 4, // Admiral Bundesliga (Austria)\n  207: 4, // Super League (Switzerland)\n  197: 4, // Super League (Super League 1) (Greece)\n  106: 4, // Ekstraklasa (Poland)\n  119: 4, // Superliga (Denmark)\n  235: 4, // Premier League (Russia)\n  286: 4, // Super Liga (Serbia)\n  210: 4, // 1. HNL (HNL) (Croatia)\n  333: 4, // Premier League (Ukraine)\n  283: 4, // Liga 1 (Liga I) (Romania)\n  271: 4, // OTP Bank Liga (NB I) (Hungary)\n  345: 4, // Chance Národní Liga (Czech Liga) (Czech Republic)\n  332: 4, // Niké Liga (Super Liga) (Slovakia)\n  315: 4, // Premier Liga (Premijer Liga) (Bosnia & Herzegovina)\n  239: 4, // Liga BetPlay (Primera A) (Colombia)\n  265: 4, // Primera Division (Chile)\n  281: 4, // Primera Division (Peru)\n  250: 4, // Division 1 (Primera División) - Apertura (Paraguay)\n  252: 4, // Division 1 (Primera División) - Clausura (Paraguay)\n  268: 4, // Primera Division - Apertura (Uruguay)\n  270: 4, // Primera Division - Clausura (Uruguay)\n  242: 4, // Liga Pro (Ecuador)\n  292: 4, // K League 1 (South Korea)\n  98: 4, // J-League (J1 League) (Japan)\n  169: 4, // Super League (China)\n  305: 4, // Stars League (Qatar)\n  301: 4, // Uae League (Pro League) (UAE)\n  200: 4, // Botola Pro (Morocco)\n  202: 4, // Ligue 1 (Tunisia)\n  186: 4, // Ligue 1 (Algeria)\n  233: 4, // Premier League (Egypt)\n  288: 4, // Premier League (Premier Soccer League) (South Africa)\n  525: 4, // UEFA Champions League Women\n  254: 4, // NWSL (USA Women)\n  44: 4, // FA WSL (England Women)\n  480: 4, // Olympic Games (Men)\n  524: 4, // Olympic Games (Women)\n  22: 4, // CONCACAF Gold Cup\n};\n\n/**\n * Priority map for leagues within each tier\n * Lower number means higher priority within the tier\n * This ensures leagues are displayed in a specific order within each tier\n */\nexport const leaguePriorityMap: Record<number, number> = {\n  // Tier 1 leagues in order of priority\n  1: 1,   // World Cup (FIFA) \n  8: 2,   // World Cup Women (FIFA)\n  4: 3,   // European Championship (UEFA Euro)\n  2: 4,   // Champions League (UEFA)\n  3: 5,   // Europa League (UEFA)\n  \n  // Tier 2 leagues in order of priority (Big 5 European Leagues)\n  39: 1,  // Premier League (England)\n  140: 2, // La Liga (Spain)\n  78: 3,  // Bundesliga (Germany)\n  135: 4, // Serie A (Italy)\n  61: 5,  // Ligue 1 (France)\n  9: 6,   // Copa America (CONMEBOL)\n  13: 7,  // Copa Libertadores (CONMEBOL)\n  6: 8,   // Africa Cup of Nations (CAF)\n  7: 9,   // AFC Asian Cup (Asia)\n  \n  // Tier 3 leagues in order of priority\n  45: 1,  // FA Cup (England)\n  143: 2, // Copa Del Rey (Spain)\n  81: 3,  // DFB Pokal (Germany)\n  137: 4, // Coppa Italia (Italy)\n  848: 5, // Europa Conference League (UEFA)\n  66: 6,  // Coupe de France (France)\n  48: 7,  // League Cup (Carabao Cup) (England)\n  71: 8,  // Serie A (Brazil)\n  128: 9, // Liga Profesional de Fútbol (Argentina)\n  253: 10, // Major League Soccer (USA)\n  307: 11, // Pro League (Saudi Arabia)\n  88: 12,  // Eredivisie (Netherlands - includes Eredivisie Playoffs)\n  94: 13,  // Liga Portugal (Primeira Liga) (Portugal)\n  203: 14, // Super Lig (Süper Lig) (Turkey)\n  15: 15,  // FIFA Club World Cup\n  32: 16,  // WC Qualification Europe (UEFA)\n  34: 17,  // WC Qualification South America (CONMEBOL)\n  30: 18,  // WC Qualification Asia (AFC)\n  29: 19,  // WC Qualification Africa (CAF)\n  31: 20,  // WC Qualification Concacaf\n  36: 21,  // Africa Cup of Nations Qualifications (CAF)\n  \n  // Tier 4 leagues in order of priority\n  40: 1,   // Championship (England - includes Championship Play-Offs)\n  79: 2,   // 2. Bundesliga (Germany)\n  141: 3,  // La Liga 2 (Segunda División) (Spain)\n  136: 4,  // Serie B (Italy)\n  62: 5,   // Ligue 2 (France)\n  188: 6,  // A-League Men (Australia)\n  262: 7,  // Liga MX (Mexico)\n  17: 8,   // AFC Champions League Elite (Asia)\n  12: 9,   // CAF Champions League (Africa)\n  856: 10, // CONCACAF Champions Cup (North/Central America & Caribbean)\n  11: 11,  // Copa Sudamericana (CONMEBOL)\n  73: 12,  // Copa do Brasil (Brazil)\n  130: 13, // Copa Argentina (Argentina)\n  144: 14, // Pro League (Jupiler Pro League) (Belgium)\n  179: 15, // Premiership Play-Offs (Scotland) - Represents the top flight implicitly\n  408: 16, // Premiership (Northern Ireland)\n  181: 17, // Scottish Cup (FA Cup) (Scotland)\n  90: 18,  // KNVB Beker (Netherlands)\n  96: 19,  // Taça De Portugal (Portugal)\n  206: 20, // Turkish Cup (Turkey)\n  218: 21, // Admiral Bundesliga (Austria)\n  207: 22, // Super League (Switzerland)\n  197: 23, // Super League (Super League 1) (Greece)\n  106: 24, // Ekstraklasa (Poland)\n  119: 25, // Superliga (Denmark)\n  235: 26, // Premier League (Russia)\n  286: 27, // Super Liga (Serbia)\n  210: 28, // 1. HNL (HNL) (Croatia)\n  333: 29, // Premier League (Ukraine)\n  283: 30, // Liga 1 (Liga I) (Romania)\n  271: 31, // OTP Bank Liga (NB I) (Hungary)\n  345: 32, // Chance Národní Liga (Czech Liga) (Czech Republic)\n  332: 33, // Niké Liga (Super Liga) (Slovakia)\n  315: 34, // Premier Liga (Premijer Liga) (Bosnia & Herzegovina)\n  239: 35, // Liga BetPlay (Primera A) (Colombia)\n  265: 36, // Primera Division (Chile)\n  281: 37, // Primera Division (Peru)\n  250: 38, // Division 1 (Primera División) - Apertura (Paraguay)\n  252: 39, // Division 1 (Primera División) - Clausura (Paraguay)\n  268: 40, // Primera Division - Apertura (Uruguay)\n  270: 41, // Primera Division - Clausura (Uruguay)\n  242: 42, // Liga Pro (Ecuador)\n  292: 43, // K League 1 (South Korea)\n  98: 44,  // J-League (J1 League) (Japan)\n  169: 45, // Super League (China)\n  305: 46, // Stars League (Qatar)\n  301: 47, // Uae League (Pro League) (UAE)\n  200: 48, // Botola Pro (Morocco)\n  202: 49, // Ligue 1 (Tunisia)\n  186: 50, // Ligue 1 (Algeria)\n  233: 51, // Premier League (Egypt)\n  288: 52, // Premier League (South Africa)\n  525: 53, // UEFA Champions League Women\n  254: 54, // NWSL (USA Women)\n  44: 55,  // FA WSL (England Women)\n  480: 56, // Olympic Games (Men)\n  524: 57, // Olympic Games (Women)\n  22: 58,  // CONCACAF Gold Cup\n};\n\n/**\n * Function to get the tier of a league\n * @param leagueId - The league ID to check\n * @returns number - The tier of the league (1-6), or 7 if not found (lowest priority)\n */\nexport function getLeagueTier(leagueId: number): number {\n  return leagueId in leagueTierMap ? leagueTierMap[leagueId] : 7;\n}\n\n/**\n * Function to get the priority of a league within its tier\n * @param leagueId - The league ID to check\n * @returns number - The priority within the tier (lower is higher priority), or 999 if not explicitly prioritized\n */\nexport function getLeaguePriority(leagueId: number): number {\n  return leagueId in leaguePriorityMap ? leaguePriorityMap[leagueId] : 999;\n}\n\n/**\n * Interface for grouped fixtures by league\n */\nexport interface LeagueGroup {\n  leagueId: number;\n  leagueName: string;\n  leagueLogo: string;\n  leagueCountry: string;\n  tier: number;\n  priority: number;\n  fixtures: any[]; // Will be typed as Fixture[] when imported\n}\n\n/**\n * Function to group fixtures by league and sort by tier/priority\n * @param fixtures - Array of fixtures to group\n * @returns LeagueGroup[] - Grouped and sorted fixtures\n */\nexport function groupFixturesByLeague(fixtures: any[]): LeagueGroup[] {\n  // Group fixtures by league ID\n  const leagueGroups = new Map<number, LeagueGroup>();\n  \n  fixtures.forEach(fixture => {\n    const leagueId = fixture.league.id;\n    \n    if (!leagueGroups.has(leagueId)) {\n      leagueGroups.set(leagueId, {\n        leagueId,\n        leagueName: fixture.league.name,\n        leagueLogo: fixture.league.logo,\n        leagueCountry: fixture.league.country,\n        tier: getLeagueTier(leagueId),\n        priority: getLeaguePriority(leagueId),\n        fixtures: []\n      });\n    }\n    \n    leagueGroups.get(leagueId)!.fixtures.push(fixture);\n  });\n  \n  // Convert to array and sort by tier, then by priority within tier\n  return Array.from(leagueGroups.values()).sort((a, b) => {\n    // First sort by tier (lower tier number = higher priority)\n    if (a.tier !== b.tier) {\n      return a.tier - b.tier;\n    }\n    // Then sort by priority within tier (lower priority number = higher priority)\n    return a.priority - b.priority;\n  });\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC,GAED,sBAAsB;;;;;;;;AACf,MAAM,gBAAwC;IACnD,qDAAqD;IACrD,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IAEH,uCAAuC;IACvC,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,GAAG;IACH,IAAI;IACJ,GAAG;IACH,GAAG;IAEH,sEAAsE;IACtE,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,IAAI;IAC<PERSON>,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,KAAK;IAEL,4DAA4D;IAC5D,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,KAAK;IACL,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,IAAI;IACJ,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,IAAI;IACJ,KAAK;IACL,KAAK;IACL,IAAI;AACN;AAOO,MAAM,oBAA4C;IACvD,sCAAsC;IACtC,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IAEH,+DAA+D;IAC/D,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,GAAG;IACH,IAAI;IACJ,GAAG;IACH,GAAG;IAEH,sCAAsC;IACtC,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IAEJ,sCAAsC;IACtC,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,KAAK;IACL,IAAI;IACJ,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,IAAI;IACJ,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,IAAI;IACJ,KAAK;IACL,KAAK;IACL,IAAI;AACN;AAOO,SAAS,cAAc,QAAgB;IAC5C,OAAO,YAAY,gBAAgB,aAAa,CAAC,SAAS,GAAG;AAC/D;AAOO,SAAS,kBAAkB,QAAgB;IAChD,OAAO,YAAY,oBAAoB,iBAAiB,CAAC,SAAS,GAAG;AACvE;AAoBO,SAAS,sBAAsB,QAAe;IACnD,8BAA8B;IAC9B,MAAM,eAAe,IAAI;IAEzB,SAAS,OAAO,CAAC,CAAA;QACf,MAAM,WAAW,QAAQ,MAAM,CAAC,EAAE;QAElC,IAAI,CAAC,aAAa,GAAG,CAAC,WAAW;YAC/B,aAAa,GAAG,CAAC,UAAU;gBACzB;gBACA,YAAY,QAAQ,MAAM,CAAC,IAAI;gBAC/B,YAAY,QAAQ,MAAM,CAAC,IAAI;gBAC/B,eAAe,QAAQ,MAAM,CAAC,OAAO;gBACrC,MAAM,cAAc;gBACpB,UAAU,kBAAkB;gBAC5B,UAAU,EAAE;YACd;QACF;QAEA,aAAa,GAAG,CAAC,UAAW,QAAQ,CAAC,IAAI,CAAC;IAC5C;IAEA,kEAAkE;IAClE,OAAO,MAAM,IAAI,CAAC,aAAa,MAAM,IAAI,IAAI,CAAC,CAAC,GAAG;QAChD,2DAA2D;QAC3D,IAAI,EAAE,IAAI,KAAK,EAAE,IAAI,EAAE;YACrB,OAAO,EAAE,IAAI,GAAG,EAAE,IAAI;QACxB;QACA,8EAA8E;QAC9E,OAAO,EAAE,QAAQ,GAAG,EAAE,QAAQ;IAChC;AACF", "debugId": null}}, {"offset": {"line": 1721, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/kickoffpredict/frontend/src/components/MainContent.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { ChevronLeft, ChevronRight, Search } from 'lucide-react';\nimport { format, addDays, subDays, isToday } from 'date-fns';\nimport { apiService, Fixture, handleApiError } from '@/lib/api';\nimport { useLiveFixtures } from '@/hooks/useSocket';\nimport { groupFixturesByLeague, LeagueGroup } from '@/lib/leagueTiers';\n\n// Remove the local Fixture interface since we're importing it from api.ts\n\ninterface MainContentProps {\n  selectedLeagueId?: number | null;\n  onClearLeagueFilter?: () => void;\n}\n\nexport default function MainContent({ selectedLeagueId, onClearLeagueFilter }: MainContentProps) {\n  const [selectedDate, setSelectedDate] = useState(() => new Date());\n  const [fixtures, setFixtures] = useState<Fixture[]>([]);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [mounted, setMounted] = useState(false);\n\n  // Use WebSocket for live fixtures\n  const { liveFixtures, connected } = useLiveFixtures();\n\n  // Set mounted state to prevent hydration mismatch\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  // Generate date navigation array\n  const generateDateArray = () => {\n    const dates = [];\n    const today = new Date();\n    today.setHours(0, 0, 0, 0); // Normalize to start of day\n    for (let i = -2; i <= 2; i++) {\n      dates.push(addDays(today, i));\n    }\n    return dates;\n  };\n\n  const dateArray = generateDateArray();\n\n  // Fetch fixtures data from API\n  useEffect(() => {\n    const fetchFixtures = async () => {\n      setLoading(true);\n      setError(null);\n\n      try {\n        const dateString = format(selectedDate, 'yyyy-MM-dd');\n        const fixturesData = await apiService.getFixtures(dateString);\n        setFixtures(fixturesData);\n      } catch (err) {\n        const errorMessage = handleApiError(err);\n        setError(errorMessage);\n        console.error('Failed to fetch fixtures:', err);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchFixtures();\n  }, [selectedDate]);\n\n  // Update fixtures with live data when available\n  useEffect(() => {\n    if (liveFixtures.length > 0) {\n      setFixtures(prevFixtures => {\n        const updatedFixtures = [...prevFixtures];\n\n        liveFixtures.forEach(liveFixture => {\n          const index = updatedFixtures.findIndex(f => f._id === liveFixture._id);\n          if (index !== -1) {\n            updatedFixtures[index] = liveFixture;\n          }\n        });\n\n        return updatedFixtures;\n      });\n    }\n  }, [liveFixtures]);\n\n  // Filter fixtures based on search query and selected league\n  const filteredFixtures = fixtures.filter(fixture => {\n    // Filter by league if one is selected\n    if (selectedLeagueId && fixture.league.id !== selectedLeagueId) {\n      return false;\n    }\n\n    // Filter by search query if one exists\n    if (!searchQuery) return true;\n\n    const query = searchQuery.toLowerCase();\n    return (\n      fixture.teams.home.name.toLowerCase().includes(query) ||\n      fixture.teams.away.name.toLowerCase().includes(query) ||\n      fixture.league.name.toLowerCase().includes(query) ||\n      fixture.league.country.toLowerCase().includes(query)\n    );\n  });\n\n  // Group fixtures by league and sort by tier/priority\n  const groupedFixtures = groupFixturesByLeague(filteredFixtures);\n\n  const handleDateChange = (date: Date) => {\n    setSelectedDate(date);\n  };\n\n  const handlePrevDate = () => {\n    setSelectedDate(subDays(selectedDate, 1));\n  };\n\n  const handleNextDate = () => {\n    setSelectedDate(addDays(selectedDate, 1));\n  };\n\n  // Function to get country flag URL using API-Sports media\n  const getCountryFlagUrl = (countryName: string) => {\n    // Convert country name to country code for API-Sports flags\n    const countryMappings: { [key: string]: string } = {\n      'england': 'gb',\n      'scotland': 'gb',\n      'wales': 'gb',\n      'northern ireland': 'gb',\n      'united kingdom': 'gb',\n      'usa': 'us',\n      'united states': 'us',\n      'south korea': 'kr',\n      'north korea': 'kp',\n      'czech republic': 'cz',\n      'bosnia and herzegovina': 'ba',\n      'trinidad and tobago': 'tt',\n      'costa rica': 'cr',\n      'el salvador': 'sv',\n      'saudi arabia': 'sa',\n      'united arab emirates': 'ae',\n      'ivory coast': 'ci',\n      'south africa': 'za',\n      'world': 'fifa'\n    };\n\n    const countryCode = countryMappings[countryName.toLowerCase()] ||\n                       countryName.toLowerCase().replace(/\\s+/g, '-').substring(0, 2);\n\n    return `https://media.api-sports.io/flags/${countryCode}.svg`;\n  };\n\n  const LeagueHeader = ({ leagueGroup }: { leagueGroup: LeagueGroup }) => (\n    <div className=\"flex items-center space-x-2 py-2 px-4 bg-muted border-b border-border\">\n      <img\n        src={getCountryFlagUrl(leagueGroup.leagueCountry)}\n        alt={leagueGroup.leagueCountry}\n        className=\"w-5 h-5 object-cover rounded-full border border-border\"\n        onError={(e) => {\n          // Keep the same circular styling even for fallback\n          e.currentTarget.src = leagueGroup.leagueLogo;\n          e.currentTarget.className = \"w-5 h-5 object-cover rounded-full border border-border\";\n        }}\n      />\n      <h3 className=\"text-sm font-medium text-foreground\">{leagueGroup.leagueCountry} - {leagueGroup.leagueName}</h3>\n    </div>\n  );\n\n  // Skeleton component for loading fixture cards\n  const FixtureCardSkeleton = () => (\n    <div className=\"bg-card border-b border-border\">\n      <div className=\"flex items-center py-3 px-4\">\n        {/* Favorite Icon and Status Column */}\n        <div className=\"flex flex-col items-center mr-3\">\n          <div className=\"w-4 h-4 bg-muted rounded animate-pulse mb-1\"></div>\n          <div className=\"w-8 h-3 bg-muted rounded animate-pulse\"></div>\n        </div>\n\n        {/* Teams Column */}\n        <div className=\"flex-1 min-w-0\">\n          {/* Home Team Row */}\n          <div className=\"flex items-center justify-between space-x-2 mb-1\">\n            <div className=\"flex items-center space-x-2 flex-1 min-w-0\">\n              <div className=\"w-6 h-6 bg-muted rounded animate-pulse flex-shrink-0\"></div>\n              <div className=\"h-4 bg-muted rounded animate-pulse w-24\"></div>\n            </div>\n            <div className=\"w-4 h-4 bg-muted rounded animate-pulse flex-shrink-0\"></div>\n          </div>\n\n          {/* Away Team Row */}\n          <div className=\"flex items-center justify-between space-x-2\">\n            <div className=\"flex items-center space-x-2 flex-1 min-w-0\">\n              <div className=\"w-6 h-6 bg-muted rounded animate-pulse flex-shrink-0\"></div>\n              <div className=\"h-4 bg-muted rounded animate-pulse w-20\"></div>\n            </div>\n            <div className=\"w-4 h-4 bg-muted rounded animate-pulse flex-shrink-0\"></div>\n          </div>\n        </div>\n\n        {/* Statistics/Odds Columns */}\n        <div className=\"flex items-center space-x-6 ml-6\">\n          <div className=\"min-w-[60px] pl-2\">\n            <div className=\"h-3 bg-muted rounded animate-pulse w-16\"></div>\n          </div>\n          <div className=\"min-w-[40px]\">\n            <div className=\"h-4 bg-muted rounded animate-pulse w-8\"></div>\n          </div>\n          <div className=\"min-w-[40px]\">\n            <div className=\"h-4 bg-muted rounded animate-pulse w-8\"></div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n\n  const FixtureCard = ({ fixture, grouped = false }: { fixture: Fixture; grouped?: boolean }) => {\n    // Function to get display status/time\n    const getStatusDisplay = () => {\n      const status = fixture.fixture.status.short;\n      const elapsed = fixture.fixture.status.elapsed;\n\n      // Live statuses with elapsed time\n      if ((status === '1H' || status === '2H' || status === 'LIVE') && elapsed) {\n        return `${elapsed}'`;\n      }\n\n      // Other match statuses\n      if (status === 'HT') return 'HT';\n      if (status === 'FT') return 'FT';\n      if (status === 'AET') return 'AET';\n      if (status === 'PEN') return 'PEN';\n      if (status === 'PST') return 'PST';\n      if (status === 'CANC') return 'CANC';\n      if (status === 'ABD') return 'ABD';\n      if (status === 'AWD') return 'AWD';\n      if (status === 'WO') return 'WO';\n      if (status === 'TBD') return 'TBD';\n\n      // For upcoming matches (NS), show time\n      if (status === 'NS') {\n        return mounted ? format(new Date(fixture.fixture.date), 'HH:mm') : '';\n      }\n\n      return status;\n    };\n\n    // Function to check if status should be red (live/in-play)\n    const isLiveStatus = () => {\n      const status = fixture.fixture.status.short;\n      return ['1H', '2H', 'LIVE', 'HT', 'ET', 'BT', 'P', 'SUSP', 'INT'].includes(status);\n    };\n\n    return (\n      <div className=\"bg-card border-b border-border hover:bg-muted/50 transition-colors\">\n        <div className=\"flex items-center py-3 px-4\">\n          {/* Favorite Icon and Status Column */}\n          <div className=\"flex flex-col items-center mr-3\">\n            {/* Favorite Icon */}\n            <button className=\"text-muted-foreground hover:text-yellow-500 transition-colors mb-1\">\n              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z\" />\n              </svg>\n            </button>\n            {/* Status/Time Display */}\n            <div className={`text-xs font-medium text-center min-w-[32px] ${\n              isLiveStatus() ? 'text-red-500' : 'text-muted-foreground'\n            }`}>\n              {getStatusDisplay()}\n            </div>\n          </div>\n\n          {/* Teams Column */}\n          <div className=\"flex-1 min-w-0\">\n            {/* Home Team Row */}\n            <div className=\"flex items-center justify-between space-x-2 mb-1\">\n              <div className=\"flex items-center space-x-2 flex-1 min-w-0\">\n                <img\n                  src={fixture.teams.home.logo}\n                  alt={fixture.teams.home.name}\n                  className=\"w-6 h-6 object-contain flex-shrink-0\"\n                  onError={(e) => {\n                    e.currentTarget.style.display = 'none';\n                  }}\n                />\n                <span className=\"text-sm text-foreground truncate\">{fixture.teams.home.name}</span>\n              </div>\n              {/* Home Score */}\n              <div className=\"text-sm font-semibold text-foreground ml-2 flex-shrink-0\">\n                {fixture.goals.home !== null ? fixture.goals.home : '-'}\n              </div>\n            </div>\n\n            {/* Away Team Row */}\n            <div className=\"flex items-center justify-between space-x-2\">\n              <div className=\"flex items-center space-x-2 flex-1 min-w-0\">\n                <img\n                  src={fixture.teams.away.logo}\n                  alt={fixture.teams.away.name}\n                  className=\"w-6 h-6 object-contain flex-shrink-0\"\n                  onError={(e) => {\n                    e.currentTarget.style.display = 'none';\n                  }}\n                />\n                <span className=\"text-sm text-foreground truncate\">{fixture.teams.away.name}</span>\n              </div>\n              {/* Away Score */}\n              <div className=\"text-sm font-semibold text-foreground ml-2 flex-shrink-0\">\n                {fixture.goals.away !== null ? fixture.goals.away : '-'}\n              </div>\n            </div>\n          </div>\n\n          {/* Statistics/Odds Columns - Centered between teams */}\n          <div className=\"flex items-center space-x-6 ml-6 text-xs\">\n            <div className=\"text-left min-w-[60px] pl-2\">\n              <div className=\"text-muted-foreground\">Number of goals</div>\n            </div>\n            <div className=\"text-center min-w-[40px]\">\n              <div className=\"text-primary font-medium\">-3.5</div>\n            </div>\n            <div className=\"text-right min-w-[40px]\">\n              <div className=\"text-foreground font-medium\">1.12</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  // Prevent hydration mismatch by not rendering until mounted\n  if (!mounted) {\n    return (\n      <div className=\"flex-1 p-6\">\n        <div className=\"max-w-4xl mx-auto\">\n          <div className=\"animate-pulse space-y-6\">\n            <div className=\"h-16 bg-muted rounded-lg\"></div>\n            <div className=\"h-32 bg-muted rounded-lg\"></div>\n            <div className=\"h-12 bg-muted rounded-lg\"></div>\n            <div className=\"space-y-4\">\n              <div className=\"h-24 bg-muted rounded-lg\"></div>\n              <div className=\"h-24 bg-muted rounded-lg\"></div>\n              <div className=\"h-24 bg-muted rounded-lg\"></div>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"flex-1 p-6\">\n      <div className=\"max-w-4xl mx-auto\">\n        {/* Date Navigation and Search Bar Combined */}\n        <div className=\"bg-card rounded-lg border border-border p-4 mb-6\">\n          {/* Date Navigation */}\n          <div className=\"flex items-center justify-between mb-4\">\n            <button\n              onClick={handlePrevDate}\n              className=\"p-2 hover:bg-muted rounded-lg transition-colors\"\n            >\n              <ChevronLeft className=\"w-5 h-5 text-muted-foreground\" />\n            </button>\n\n            <div className=\"flex items-center space-x-2\">\n              {dateArray.map((date, index) => (\n                <button\n                  key={index}\n                  onClick={() => handleDateChange(date)}\n                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${\n                    format(date, 'yyyy-MM-dd') === format(selectedDate, 'yyyy-MM-dd')\n                      ? 'bg-primary text-primary-foreground'\n                      : isToday(date)\n                      ? 'bg-primary/10 text-primary hover:bg-primary/20'\n                      : 'text-foreground hover:bg-muted'\n                  }`}\n                >\n                  <div className=\"text-center\">\n                    <div className=\"text-xs\">{format(date, 'EEE')}</div>\n                    <div>{format(date, 'd')}</div>\n                  </div>\n                </button>\n              ))}\n            </div>\n\n            <button\n              onClick={handleNextDate}\n              className=\"p-2 hover:bg-muted rounded-lg transition-colors\"\n            >\n              <ChevronRight className=\"w-5 h-5 text-muted-foreground\" />\n            </button>\n          </div>\n\n          {/* Search Bar */}\n          <div className=\"flex items-center bg-muted rounded-lg px-3 py-2\">\n            <Search className=\"w-4 h-4 text-muted-foreground mr-2\" />\n            <input\n              type=\"text\"\n              placeholder=\"Search fixtures, teams, leagues...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"bg-transparent outline-none text-sm flex-1 text-foreground placeholder-muted-foreground\"\n            />\n          </div>\n        </div>\n\n        {/* Betting Market Filter Tabs */}\n        <div className=\"flex items-center space-x-1 mb-4\">\n          <button className=\"px-4 py-2 bg-primary text-primary-foreground text-sm rounded-lg font-medium\">\n            Top tips\n          </button>\n          <button className=\"px-4 py-2 bg-muted text-foreground text-sm rounded-lg font-medium hover:bg-muted/80 transition-colors\">\n            1X2\n          </button>\n          <button className=\"px-4 py-2 bg-muted text-foreground text-sm rounded-lg font-medium hover:bg-muted/80 transition-colors\">\n            Both teams to score\n          </button>\n          <button className=\"px-4 py-2 bg-muted text-foreground text-sm rounded-lg font-medium hover:bg-muted/80 transition-colors\">\n            Under/Over 2.5\n          </button>\n          <button className=\"px-4 py-2 bg-muted text-foreground text-sm rounded-lg font-medium hover:bg-muted/80 transition-colors\">\n            Double chance\n          </button>\n          <button className=\"px-4 py-2 bg-muted text-foreground text-sm rounded-lg font-medium hover:bg-muted/80 transition-colors\">\n            Under/Over 1.5\n          </button>\n          <button className=\"px-4 py-2 bg-muted text-foreground text-sm rounded-lg font-medium hover:bg-muted/80 transition-colors\">\n            Under/Over 3.5\n          </button>\n        </div>\n\n        {/* Fixtures List */}\n        <div className=\"space-y-4\">\n          {selectedLeagueId && (\n            <div className=\"flex items-center justify-end\">\n              <button\n                onClick={onClearLeagueFilter}\n                className=\"text-sm text-primary hover:text-primary/80 font-medium\"\n              >\n                Show All Leagues\n              </button>\n            </div>\n          )}\n\n          {loading ? (\n            <div className=\"bg-card rounded-lg border border-border overflow-hidden\">\n              {/* Column Headers */}\n              <div className=\"flex items-center py-2 px-4 bg-muted border-b border-border text-xs text-muted-foreground font-medium\">\n                <div className=\"mr-3 w-10\"></div>\n                <div className=\"flex-1\"></div>\n                <div className=\"flex items-center space-x-6 ml-6\">\n                  <div className=\"text-left min-w-[60px] pl-2\">Market</div>\n                  <div className=\"text-center min-w-[40px]\">Value</div>\n                  <div className=\"text-right min-w-[40px]\">Odds</div>\n                </div>\n              </div>\n              {/* Skeleton fixture cards */}\n              {Array.from({ length: 8 }).map((_, index) => (\n                <FixtureCardSkeleton key={`fixture-skeleton-${index}`} />\n              ))}\n            </div>\n          ) : error ? (\n            <div className=\"text-center py-8\">\n              <div className=\"text-destructive mb-2\">Error loading fixtures</div>\n              <div className=\"text-muted-foreground text-sm\">{error}</div>\n              <button\n                onClick={() => window.location.reload()}\n                className=\"mt-4 px-4 py-2 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-colors\"\n              >\n                Retry\n              </button>\n            </div>\n          ) : groupedFixtures.length > 0 ? (\n            <div className=\"bg-card rounded-lg border border-border overflow-hidden\">\n              {/* Column Headers */}\n              <div className=\"flex items-center py-2 px-4 bg-muted border-b border-border text-xs text-muted-foreground font-medium\">\n                {/* Favorite Icon Column Space */}\n                <div className=\"mr-3 w-10\"></div>\n                {/* Teams Column - No header text */}\n                <div className=\"flex-1\"></div>\n                {/* Statistics Headers - Right aligned with data */}\n                <div className=\"flex items-center space-x-6 ml-6\">\n                  <div className=\"text-left min-w-[60px] pl-2\">Market</div>\n                  <div className=\"text-center min-w-[40px]\">Value</div>\n                  <div className=\"text-right min-w-[40px]\">Odds</div>\n                </div>\n              </div>\n\n              {groupedFixtures.map((leagueGroup) => (\n                <div key={leagueGroup.leagueId}>\n                  <LeagueHeader leagueGroup={leagueGroup} />\n                  <div>\n                    {leagueGroup.fixtures.map((fixture) => (\n                      <FixtureCard key={fixture._id || fixture.id} fixture={fixture} grouped={true} />\n                    ))}\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : searchQuery ? (\n            <div className=\"text-center py-8\">\n              <div className=\"text-muted-foreground\">No fixtures found matching \"{searchQuery}\"</div>\n              <div className=\"text-muted-foreground/60 text-sm mt-2\">\n                Try a different search term or clear the search to see all fixtures.\n              </div>\n            </div>\n          ) : (\n            <div className=\"text-center py-8\">\n              <div className=\"text-muted-foreground\">No fixtures found for this date.</div>\n              <div className=\"text-muted-foreground/60 text-sm mt-2\">\n                Try selecting a different date or check back later.\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AAPA;;;;;;;AAgBe,SAAS,YAAY,KAA2D;QAA3D,EAAE,gBAAgB,EAAE,mBAAmB,EAAoB,GAA3D;;IAClC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;gCAAE,IAAM,IAAI;;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,kCAAkC;IAClC,MAAM,EAAE,YAAY,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD;IAElD,kDAAkD;IAClD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,WAAW;QACb;gCAAG,EAAE;IAEL,iCAAiC;IACjC,MAAM,oBAAoB;QACxB,MAAM,QAAQ,EAAE;QAChB,MAAM,QAAQ,IAAI;QAClB,MAAM,QAAQ,CAAC,GAAG,GAAG,GAAG,IAAI,4BAA4B;QACxD,IAAK,IAAI,IAAI,CAAC,GAAG,KAAK,GAAG,IAAK;YAC5B,MAAM,IAAI,CAAC,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,OAAO;QAC5B;QACA,OAAO;IACT;IAEA,MAAM,YAAY;IAElB,+BAA+B;IAC/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;uDAAgB;oBACpB,WAAW;oBACX,SAAS;oBAET,IAAI;wBACF,MAAM,aAAa,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,cAAc;wBACxC,MAAM,eAAe,MAAM,oHAAA,CAAA,aAAU,CAAC,WAAW,CAAC;wBAClD,YAAY;oBACd,EAAE,OAAO,KAAK;wBACZ,MAAM,eAAe,CAAA,GAAA,oHAAA,CAAA,iBAAc,AAAD,EAAE;wBACpC,SAAS;wBACT,QAAQ,KAAK,CAAC,6BAA6B;oBAC7C,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;gCAAG;QAAC;KAAa;IAEjB,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,aAAa,MAAM,GAAG,GAAG;gBAC3B;6CAAY,CAAA;wBACV,MAAM,kBAAkB;+BAAI;yBAAa;wBAEzC,aAAa,OAAO;qDAAC,CAAA;gCACnB,MAAM,QAAQ,gBAAgB,SAAS;mEAAC,CAAA,IAAK,EAAE,GAAG,KAAK,YAAY,GAAG;;gCACtE,IAAI,UAAU,CAAC,GAAG;oCAChB,eAAe,CAAC,MAAM,GAAG;gCAC3B;4BACF;;wBAEA,OAAO;oBACT;;YACF;QACF;gCAAG;QAAC;KAAa;IAEjB,4DAA4D;IAC5D,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA;QACvC,sCAAsC;QACtC,IAAI,oBAAoB,QAAQ,MAAM,CAAC,EAAE,KAAK,kBAAkB;YAC9D,OAAO;QACT;QAEA,uCAAuC;QACvC,IAAI,CAAC,aAAa,OAAO;QAEzB,MAAM,QAAQ,YAAY,WAAW;QACrC,OACE,QAAQ,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,UAC/C,QAAQ,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,UAC/C,QAAQ,MAAM,CAAC,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,UAC3C,QAAQ,MAAM,CAAC,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC;IAElD;IAEA,qDAAqD;IACrD,MAAM,kBAAkB,CAAA,GAAA,4HAAA,CAAA,wBAAqB,AAAD,EAAE;IAE9C,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;IAClB;IAEA,MAAM,iBAAiB;QACrB,gBAAgB,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,cAAc;IACxC;IAEA,MAAM,iBAAiB;QACrB,gBAAgB,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,cAAc;IACxC;IAEA,0DAA0D;IAC1D,MAAM,oBAAoB,CAAC;QACzB,4DAA4D;QAC5D,MAAM,kBAA6C;YACjD,WAAW;YACX,YAAY;YACZ,SAAS;YACT,oBAAoB;YACpB,kBAAkB;YAClB,OAAO;YACP,iBAAiB;YACjB,eAAe;YACf,eAAe;YACf,kBAAkB;YAClB,0BAA0B;YAC1B,uBAAuB;YACvB,cAAc;YACd,eAAe;YACf,gBAAgB;YAChB,wBAAwB;YACxB,eAAe;YACf,gBAAgB;YAChB,SAAS;QACX;QAEA,MAAM,cAAc,eAAe,CAAC,YAAY,WAAW,GAAG,IAC3C,YAAY,WAAW,GAAG,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,GAAG;QAE/E,OAAO,AAAC,qCAAgD,OAAZ,aAAY;IAC1D;IAEA,MAAM,eAAe;YAAC,EAAE,WAAW,EAAgC;6BACjE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBACC,KAAK,kBAAkB,YAAY,aAAa;oBAChD,KAAK,YAAY,aAAa;oBAC9B,WAAU;oBACV,SAAS,CAAC;wBACR,mDAAmD;wBACnD,EAAE,aAAa,CAAC,GAAG,GAAG,YAAY,UAAU;wBAC5C,EAAE,aAAa,CAAC,SAAS,GAAG;oBAC9B;;;;;;8BAEF,6LAAC;oBAAG,WAAU;;wBAAuC,YAAY,aAAa;wBAAC;wBAAI,YAAY,UAAU;;;;;;;;;;;;;;IAI7G,+CAA+C;IAC/C,MAAM,sBAAsB,kBAC1B,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;;;;;;kDAEjB,6LAAC;wCAAI,WAAU;;;;;;;;;;;;0CAIjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;;;;;;kDAEjB,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;;kCAKnB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;0CAEjB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;0CAEjB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOzB,MAAM,cAAc;YAAC,EAAE,OAAO,EAAE,UAAU,KAAK,EAA2C;QACxF,sCAAsC;QACtC,MAAM,mBAAmB;YACvB,MAAM,SAAS,QAAQ,OAAO,CAAC,MAAM,CAAC,KAAK;YAC3C,MAAM,UAAU,QAAQ,OAAO,CAAC,MAAM,CAAC,OAAO;YAE9C,kCAAkC;YAClC,IAAI,CAAC,WAAW,QAAQ,WAAW,QAAQ,WAAW,MAAM,KAAK,SAAS;gBACxE,OAAO,AAAC,GAAU,OAAR,SAAQ;YACpB;YAEA,uBAAuB;YACvB,IAAI,WAAW,MAAM,OAAO;YAC5B,IAAI,WAAW,MAAM,OAAO;YAC5B,IAAI,WAAW,OAAO,OAAO;YAC7B,IAAI,WAAW,OAAO,OAAO;YAC7B,IAAI,WAAW,OAAO,OAAO;YAC7B,IAAI,WAAW,QAAQ,OAAO;YAC9B,IAAI,WAAW,OAAO,OAAO;YAC7B,IAAI,WAAW,OAAO,OAAO;YAC7B,IAAI,WAAW,MAAM,OAAO;YAC5B,IAAI,WAAW,OAAO,OAAO;YAE7B,uCAAuC;YACvC,IAAI,WAAW,MAAM;gBACnB,OAAO,UAAU,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,QAAQ,OAAO,CAAC,IAAI,GAAG,WAAW;YACrE;YAEA,OAAO;QACT;QAEA,2DAA2D;QAC3D,MAAM,eAAe;YACnB,MAAM,SAAS,QAAQ,OAAO,CAAC,MAAM,CAAC,KAAK;YAC3C,OAAO;gBAAC;gBAAM;gBAAM;gBAAQ;gBAAM;gBAAM;gBAAM;gBAAK;gBAAQ;aAAM,CAAC,QAAQ,CAAC;QAC7E;QAEA,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAO,WAAU;0CAChB,cAAA,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAIzE,6LAAC;gCAAI,WAAW,AAAC,gDAEhB,OADC,iBAAiB,iBAAiB;0CAEjC;;;;;;;;;;;;kCAKL,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,KAAK,QAAQ,KAAK,CAAC,IAAI,CAAC,IAAI;gDAC5B,KAAK,QAAQ,KAAK,CAAC,IAAI,CAAC,IAAI;gDAC5B,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;gDAClC;;;;;;0DAEF,6LAAC;gDAAK,WAAU;0DAAoC,QAAQ,KAAK,CAAC,IAAI,CAAC,IAAI;;;;;;;;;;;;kDAG7E,6LAAC;wCAAI,WAAU;kDACZ,QAAQ,KAAK,CAAC,IAAI,KAAK,OAAO,QAAQ,KAAK,CAAC,IAAI,GAAG;;;;;;;;;;;;0CAKxD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,KAAK,QAAQ,KAAK,CAAC,IAAI,CAAC,IAAI;gDAC5B,KAAK,QAAQ,KAAK,CAAC,IAAI,CAAC,IAAI;gDAC5B,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,aAAa,CAAC,KAAK,CAAC,OAAO,GAAG;gDAClC;;;;;;0DAEF,6LAAC;gDAAK,WAAU;0DAAoC,QAAQ,KAAK,CAAC,IAAI,CAAC,IAAI;;;;;;;;;;;;kDAG7E,6LAAC;wCAAI,WAAU;kDACZ,QAAQ,KAAK,CAAC,IAAI,KAAK,OAAO,QAAQ,KAAK,CAAC,IAAI,GAAG;;;;;;;;;;;;;;;;;;kCAM1D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CAAwB;;;;;;;;;;;0CAEzC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CAA2B;;;;;;;;;;;0CAE5C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;8CAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMzD;IAEA,4DAA4D;IAC5D,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAM3B;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;8CAGzB,6LAAC;oCAAI,WAAU;8CACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,6LAAC;4CAEC,SAAS,IAAM,iBAAiB;4CAChC,WAAW,AAAC,8DAMX,OALC,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,kBAAkB,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,cAAc,gBAChD,uCACA,CAAA,GAAA,yIAAA,CAAA,UAAO,AAAD,EAAE,QACR,mDACA;sDAGN,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAW,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;;;;;;kEACvC,6LAAC;kEAAK,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;;;;;;;;;;;;2CAZhB;;;;;;;;;;8CAkBX,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAK5B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,WAAU;;;;;;;;;;;;;;;;;;8BAMhB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAO,WAAU;sCAA8E;;;;;;sCAGhG,6LAAC;4BAAO,WAAU;sCAAwG;;;;;;sCAG1H,6LAAC;4BAAO,WAAU;sCAAwG;;;;;;sCAG1H,6LAAC;4BAAO,WAAU;sCAAwG;;;;;;sCAG1H,6LAAC;4BAAO,WAAU;sCAAwG;;;;;;sCAG1H,6LAAC;4BAAO,WAAU;sCAAwG;;;;;;sCAG1H,6LAAC;4BAAO,WAAU;sCAAwG;;;;;;;;;;;;8BAM5H,6LAAC;oBAAI,WAAU;;wBACZ,kCACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;wBAMJ,wBACC,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAA8B;;;;;;8DAC7C,6LAAC;oDAAI,WAAU;8DAA2B;;;;;;8DAC1C,6LAAC;oDAAI,WAAU;8DAA0B;;;;;;;;;;;;;;;;;;gCAI5C,MAAM,IAAI,CAAC;oCAAE,QAAQ;gCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6LAAC,yBAAyB,AAAC,oBAAyB,OAAN;;;;;;;;;;mCAGhD,sBACF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAwB;;;;;;8CACvC,6LAAC;oCAAI,WAAU;8CAAiC;;;;;;8CAChD,6LAAC;oCACC,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;oCACrC,WAAU;8CACX;;;;;;;;;;;mCAID,gBAAgB,MAAM,GAAG,kBAC3B,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;;;;;sDAEf,6LAAC;4CAAI,WAAU;;;;;;sDAEf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAA8B;;;;;;8DAC7C,6LAAC;oDAAI,WAAU;8DAA2B;;;;;;8DAC1C,6LAAC;oDAAI,WAAU;8DAA0B;;;;;;;;;;;;;;;;;;gCAI5C,gBAAgB,GAAG,CAAC,CAAC,4BACpB,6LAAC;;0DACC,6LAAC;gDAAa,aAAa;;;;;;0DAC3B,6LAAC;0DACE,YAAY,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACzB,6LAAC;wDAA4C,SAAS;wDAAS,SAAS;uDAAtD,QAAQ,GAAG,IAAI,QAAQ,EAAE;;;;;;;;;;;uCAJvC,YAAY,QAAQ;;;;;;;;;;mCAUhC,4BACF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;wCAAwB;wCAA6B;wCAAY;;;;;;;8CAChF,6LAAC;oCAAI,WAAU;8CAAwC;;;;;;;;;;;iDAKzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAwB;;;;;;8CACvC,6LAAC;oCAAI,WAAU;8CAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrE;GAnfwB;;QASc,4HAAA,CAAA,kBAAe;;;KAT7B", "debugId": null}}, {"offset": {"line": 2915, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/kickoffpredict/frontend/src/components/Footer.tsx"], "sourcesContent": ["'use client';\n\nexport default function Footer() {\n  return (\n    <footer className=\"bg-card border-t border-border mt-12\">\n      <div className=\"max-w-7xl mx-auto px-4 py-8\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          {/* Company Info */}\n          <div className=\"col-span-1 md:col-span-2\">\n            <div className=\"flex items-center space-x-2 mb-4\">\n              <div className=\"w-8 h-8 bg-primary rounded-full flex items-center justify-center\">\n                <span className=\"text-primary-foreground font-bold text-sm\">K</span>\n              </div>\n              <span className=\"text-xl font-bold text-foreground\">KickoffScore</span>\n            </div>\n            <p className=\"text-muted-foreground text-sm mb-4 max-w-md\">\n              Your ultimate destination for live football scores, predictions, and comprehensive match analysis.\n              Stay updated with real-time fixtures and expert betting insights.\n            </p>\n            <div className=\"flex space-x-4\">\n              <a href=\"#\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n                <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z\"/>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n                <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M22.46 6c-.77.35-1.6.58-2.46.69.88-.53 1.56-1.37 1.88-2.38-.83.5-1.75.85-2.72 1.05C18.37 4.5 17.26 4 16 4c-2.35 0-4.27 1.92-4.27 4.29 0 .34.04.67.11.98C8.28 9.09 5.11 7.38 3 4.79c-.37.63-.58 1.37-.58 2.15 0 1.49.75 2.81 1.91 3.56-.71 0-1.37-.2-1.95-.5v.03c0 2.08 1.48 3.82 3.44 4.21a4.22 4.22 0 0 1-1.93.07 4.28 4.28 0 0 0 4 2.98 8.521 8.521 0 0 1-5.33 1.84c-.34 0-.68-.02-1.02-.06C3.44 20.29 5.7 21 8.12 21 16 21 20.33 14.46 20.33 8.79c0-.19 0-.37-.01-.56.84-.6 1.56-1.36 2.14-2.23z\"/>\n                </svg>\n              </a>\n              <a href=\"#\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n                <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.75-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24.009c6.624 0 11.99-5.367 11.99-11.988C24.007 5.367 18.641.001.012.001z\"/>\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-sm font-semibold text-foreground uppercase tracking-wider mb-4\">\n              Quick Links\n            </h3>\n            <ul className=\"space-y-2\">\n              <li>\n                <a href=\"#\" className=\"text-muted-foreground hover:text-primary text-sm transition-colors\">\n                  Live Scores\n                </a>\n              </li>\n              <li>\n                <a href=\"#\" className=\"text-muted-foreground hover:text-primary text-sm transition-colors\">\n                  Fixtures\n                </a>\n              </li>\n              <li>\n                <a href=\"#\" className=\"text-muted-foreground hover:text-primary text-sm transition-colors\">\n                  Predictions\n                </a>\n              </li>\n              <li>\n                <a href=\"#\" className=\"text-muted-foreground hover:text-primary text-sm transition-colors\">\n                  League Tables\n                </a>\n              </li>\n              <li>\n                <a href=\"#\" className=\"text-muted-foreground hover:text-primary text-sm transition-colors\">\n                  News\n                </a>\n              </li>\n            </ul>\n          </div>\n\n          {/* Support */}\n          <div>\n            <h3 className=\"text-sm font-semibold text-foreground uppercase tracking-wider mb-4\">\n              Support\n            </h3>\n            <ul className=\"space-y-2\">\n              <li>\n                <a href=\"#\" className=\"text-muted-foreground hover:text-primary text-sm transition-colors\">\n                  Help Center\n                </a>\n              </li>\n              <li>\n                <a href=\"#\" className=\"text-muted-foreground hover:text-primary text-sm transition-colors\">\n                  Contact Us\n                </a>\n              </li>\n              <li>\n                <a href=\"#\" className=\"text-muted-foreground hover:text-primary text-sm transition-colors\">\n                  Privacy Policy\n                </a>\n              </li>\n              <li>\n                <a href=\"#\" className=\"text-muted-foreground hover:text-primary text-sm transition-colors\">\n                  Terms of Service\n                </a>\n              </li>\n              <li>\n                <a href=\"#\" className=\"text-muted-foreground hover:text-primary text-sm transition-colors\">\n                  API Documentation\n                </a>\n              </li>\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom Bar */}\n        <div className=\"border-t border-border mt-8 pt-8 flex flex-col md:flex-row justify-between items-center\">\n          <p className=\"text-muted-foreground text-sm\">\n            © 2024 KickoffScore. All rights reserved.\n          </p>\n          <div className=\"flex items-center space-x-6 mt-4 md:mt-0\">\n            <span className=\"text-muted-foreground text-sm\">Powered by API-Football</span>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n              <span className=\"text-primary text-sm font-medium\">Live Data</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEe,SAAS;IACtB,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAA4C;;;;;;;;;;;sDAE9D,6LAAC;4CAAK,WAAU;sDAAoC;;;;;;;;;;;;8CAEtD,6LAAC;oCAAE,WAAU;8CAA8C;;;;;;8CAI3D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,MAAK;4CAAI,WAAU;sDACpB,cAAA,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;0DACnD,cAAA,6LAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAGZ,6LAAC;4CAAE,MAAK;4CAAI,WAAU;sDACpB,cAAA,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;0DACnD,cAAA,6LAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAGZ,6LAAC;4CAAE,MAAK;4CAAI,WAAU;sDACpB,cAAA,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;0DACnD,cAAA,6LAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOhB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAsE;;;;;;8CAGpF,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDACC,cAAA,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DAAqE;;;;;;;;;;;sDAI7F,6LAAC;sDACC,cAAA,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DAAqE;;;;;;;;;;;sDAI7F,6LAAC;sDACC,cAAA,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DAAqE;;;;;;;;;;;sDAI7F,6LAAC;sDACC,cAAA,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DAAqE;;;;;;;;;;;sDAI7F,6LAAC;sDACC,cAAA,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DAAqE;;;;;;;;;;;;;;;;;;;;;;;sCAQjG,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAsE;;;;;;8CAGpF,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDACC,cAAA,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DAAqE;;;;;;;;;;;sDAI7F,6LAAC;sDACC,cAAA,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DAAqE;;;;;;;;;;;sDAI7F,6LAAC;sDACC,cAAA,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DAAqE;;;;;;;;;;;sDAI7F,6LAAC;sDACC,cAAA,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DAAqE;;;;;;;;;;;sDAI7F,6LAAC;sDACC,cAAA,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DAAqE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASnG,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAAgC;;;;;;sCAG7C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAgC;;;;;;8CAChD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAK,WAAU;sDAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjE;KAzHwB", "debugId": null}}, {"offset": {"line": 3348, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Downloads/kickoffpredict/frontend/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Header from '@/components/Header';\nimport Sidebar from '@/components/Sidebar';\nimport MainContent from '@/components/MainContent';\nimport Footer from '@/components/Footer';\n\nexport default function Home() {\n  const [isSidebarOpen, setIsSidebarOpen] = useState(false); // Start with false to prevent hydration mismatch\n  const [selectedLeagueId, setSelectedLeagueId] = useState<number | null>(null);\n\n  // Handle responsive sidebar\n  useEffect(() => {\n    const handleResize = () => {\n      if (window.innerWidth < 768) {\n        setIsSidebarOpen(false);\n      } else {\n        setIsSidebarOpen(true);\n      }\n    };\n\n    handleResize();\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  const toggleSidebar = () => {\n    setIsSidebarOpen(!isSidebarOpen);\n  };\n\n  const handleLeagueSelect = (leagueId: number) => {\n    setSelectedLeagueId(selectedLeagueId === leagueId ? null : leagueId);\n  };\n\n  const handleClearLeagueFilter = () => {\n    setSelectedLeagueId(null);\n  };\n\n\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <Header onToggleSidebar={toggleSidebar} />\n\n      <div className=\"flex gap-2\">\n        {/* Sidebar */}\n        <div className={`${isSidebarOpen ? 'block' : 'hidden'} md:block`}>\n          <Sidebar\n            onLeagueSelect={handleLeagueSelect}\n            selectedLeagueId={selectedLeagueId}\n          />\n        </div>\n\n        {/* Main Content */}\n        <div className=\"flex-1\">\n          <MainContent\n            selectedLeagueId={selectedLeagueId}\n            onClearLeagueFilter={handleClearLeagueFilter}\n          />\n        </div>\n      </div>\n\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,iDAAiD;IAC5G,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAExE,4BAA4B;IAC5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM;+CAAe;oBACnB,IAAI,OAAO,UAAU,GAAG,KAAK;wBAC3B,iBAAiB;oBACnB,OAAO;wBACL,iBAAiB;oBACnB;gBACF;;YAEA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC;kCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;yBAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,iBAAiB,CAAC;IACpB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,oBAAoB,qBAAqB,WAAW,OAAO;IAC7D;IAEA,MAAM,0BAA0B;QAC9B,oBAAoB;IACtB;IAIA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+HAAA,CAAA,UAAM;gBAAC,iBAAiB;;;;;;0BAEzB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAW,AAAC,GAAqC,OAAnC,gBAAgB,UAAU,UAAS;kCACpD,cAAA,6LAAC,gIAAA,CAAA,UAAO;4BACN,gBAAgB;4BAChB,kBAAkB;;;;;;;;;;;kCAKtB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,oIAAA,CAAA,UAAW;4BACV,kBAAkB;4BAClB,qBAAqB;;;;;;;;;;;;;;;;;0BAK3B,6LAAC,+HAAA,CAAA,UAAM;;;;;;;;;;;AAGb;GA1DwB;KAAA", "debugId": null}}]}